from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON>ey, <PERSON>olean, Enum, Float, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .db_connection import Base
import enum
from app.core.models.user import User  # Import User from core models
from app.core.models.permission import DBRole  # Import DBRole from permission models
from app.core.models.user_role import UserRole  # Import UserRole from user_role models

# Énumérations
class RepairStatusEnum(enum.Enum):
    PENDING = "PENDING"  # En attente
    IN_PROGRESS = "IN_PROGRESS"  # En cours
    ON_HOLD = "ON_HOLD"  # En pause
    COMPLETED = "COMPLETED"  # Terminé
    CANCELLED = "CANCELLED"  # Annulé

# Import other models that were already here
class RepairOrder(Base):
    __tablename__ = "repair_orders"

    id = Column(Integer, primary_key=True, index=True)
    number = Column(String, unique=True, index=True)
    status = Column(Enum(RepairStatusEnum))
    creation_date = Column(DateTime, server_default=func.now())
    scheduled_date = Column(DateTime, nullable=True)
    completion_date = Column(DateTime, nullable=True)
    description = Column(Text)
    reported_issue = Column(Text)
    brand = Column(String)
    model = Column(String)
    serial_number = Column(String, nullable=True)
    total_cost = Column(Float, default=0.0)
    labor_cost = Column(Float, default=0.0)
    parts_cost = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    discount_amount = Column(Float, default=0.0)
    final_amount = Column(Float, default=0.0)
    invoice_number = Column(String, nullable=True)
    invoice_date = Column(DateTime, nullable=True)
    payment_status = Column(String, default="pending")
    payment_method = Column(String, nullable=True)
    payment_date = Column(DateTime, nullable=True)
    credit_terms = Column(String, nullable=True)
    due_date = Column(DateTime, nullable=True)
    technician_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    warranty = Column(Boolean, default=False)
    attachments = Column(JSON)
    notes = Column(Text, nullable=True)
    customer_signature = Column(String, nullable=True)
    quality_check = Column(JSON, nullable=True)
    total_paid = Column(Float, default=0.0)

    # Relations
    technician = relationship("User", back_populates="repair_orders")
    equipment = relationship("Equipment", back_populates="repair_orders")
    customer = relationship("Customer", back_populates="repair_orders")
    diagnosis = relationship("DiagnosisResult", back_populates="repair_order")
    tasks = relationship("RepairTask", back_populates="repair_order")
    used_parts = relationship("UsedPart", back_populates="repair_order")

class DiagnosisResult(Base):
    __tablename__ = "diagnosis_results"

    id = Column(Integer, primary_key=True, index=True)
    repair_order_id = Column(Integer, ForeignKey("repair_orders.id"))
    technician_id = Column(Integer, ForeignKey("users.id"))
    diagnosis_date = Column(DateTime, server_default=func.now())
    findings = Column(Text)
    estimated_duration = Column(Integer)  # en minutes
    required_parts = Column(JSON)
    estimated_cost = Column(Float)
    recommendations = Column(Text)
    attachments = Column(JSON)

    # Relations
    repair_order = relationship("RepairOrder", back_populates="diagnosis")

class RepairTask(Base):
    __tablename__ = "repair_tasks"

    id = Column(Integer, primary_key=True, index=True)
    repair_order_id = Column(Integer, ForeignKey("repair_orders.id"))
    description = Column(Text)
    estimated_duration = Column(Integer)
    actual_duration = Column(Integer, nullable=True)
    technician_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    status = Column(String)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    notes = Column(Text, nullable=True)

    # Relations
    repair_order = relationship("RepairOrder", back_populates="tasks")

class UsedPart(Base):
    __tablename__ = "used_parts"

    id = Column(Integer, primary_key=True, index=True)
    repair_order_id = Column(Integer, ForeignKey("repair_orders.id"))
    part_id = Column(Integer, ForeignKey("parts.id"))
    quantity = Column(Integer)
    unit_price = Column(Float)
    used_at = Column(DateTime, server_default=func.now())
    technician_id = Column(Integer, ForeignKey("users.id"))
    lot_number = Column(String, nullable=True)

    # Relations
    repair_order = relationship("RepairOrder", back_populates="used_parts")
    part = relationship("Part")