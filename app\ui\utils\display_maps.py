from app.core.models.repair import RepairStatus, RepairPriority, PaymentStatus, PaymentMethod

# Centralized display labels for UI

STATUS_LABELS = {
    RepairStatus.PENDING: "En attente",
    RepairStatus.IN_PROGRESS: "En cours",
    RepairStatus.ON_HOLD: "En pause",
    RepairStatus.COMPLETED: "Terminé",
    RepairStatus.CANCELLED: "Annulé",
}

PRIORITY_LABELS = {
    RepairPriority.CRITICAL: "Critique",
    RepairPriority.HIGH: "Haute",
    RepairPriority.NORMAL: "Normale",
    RepairPriority.LOW: "Basse",
}

PAYMENT_STATUS_LABELS = {
    PaymentStatus.PENDING: "En attente",
    PaymentStatus.PARTIAL: "Partiel",
    PaymentStatus.PAID: "Payé",
    PaymentStatus.OVERDUE: "En retard",
    PaymentStatus.CANCELLED: "Annulé",
    PaymentStatus.DELIVERED: "Livrée",
}

PAYMENT_METHOD_LABELS = {
    PaymentMethod.cash: "Espèces",
    PaymentMethod.credit_card: "Carte de crédit",
    PaymentMethod.bank_transfer: "Virement bancaire",
    PaymentMethod.check: "Chèque",
    PaymentMethod.credit: "Crédit",
}

def status_label(status) -> str:
    return STATUS_LABELS.get(status, str(status))

def priority_label(priority) -> str:
    return PRIORITY_LABELS.get(priority, str(priority))

def payment_status_label(status) -> str:
    return PAYMENT_STATUS_LABELS.get(status, str(status))

def payment_method_label(method) -> str:
    # Handle both enum and raw string values
    if isinstance(method, str):
        for key, label in PAYMENT_METHOD_LABELS.items():
            if key.value == method:
                return label
        return method
    return PAYMENT_METHOD_LABELS.get(method, str(method))