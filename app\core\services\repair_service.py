from typing import List, Optional, Dict, Any, Union
import os
from sqlalchemy.orm import Session
from ..models.repair import RepairOrder, RepairOrderPydantic, RepairStatus, PaymentMethod, PaymentStatus, RepairPayment, RepairPaymentPydantic
from ..models.treasury import CashRegister, CashTransaction, TransactionCategory, CashRegisterType, PaymentMethod as TreasuryPaymentMethod
from ..models.repair_status_history import RepairStatusHistory, RepairStatusHistoryPydantic
from ..models.repair_photo import RepairPhoto, RepairPhotoPydantic, PhotoType
from .base_service import BaseService
from .inventory_service import InventoryService
from .customer_service import CustomerService
from datetime import datetime, timezone
from app.utils.pdf_generator import RepairPDFGenerator

class RepairService(BaseService[RepairOrder, RepairOrderPydantic, RepairOrderPydantic]):
    def __init__(self, db: Session = None):
        from app.utils.database import SessionLocal
        if db is None:
            db = SessionLocal()
        super().__init__(db, RepairOrder)
        self.inventory_service = InventoryService(db)
        from app.core.services.treasury_service import TreasuryService
        self.treasury_service = TreasuryService(db)

    async def assign_technician(self, repair_id: int, technician_id: int) -> bool:
        repair = await self.get(repair_id)
        if not repair:
            return False

        repair.technician_id = technician_id
        repair.status = RepairStatus.IN_PROGRESS
        self.db.commit()
        return True

    async def update_status(
        self,
        repair_id: int,
        new_status: RepairStatus,
        notes: Optional[str] = None,
        changed_by: Optional[int] = None,
        changed_at: Optional[datetime] = None
    ) -> bool:
        """
        Met à jour le statut d'une réparation et enregistre l'historique.

        Args:
            repair_id: ID de la réparation
            new_status: Nouveau statut
            notes: Notes sur le changement de statut
            changed_by: ID de l'utilisateur qui a effectué le changement
            changed_at: Date et heure du changement (par défaut: maintenant)

        Returns:
            True si la mise à jour a réussi, False sinon
        """
        repair = await self.get(repair_id)
        if not repair:
            return False

        # Enregistrer l'ancien statut dans l'historique
        old_status = repair.status

        # Mettre à jour le statut
        repair.status = new_status

        # Ajouter les notes à la réparation si fournies
        if notes:
            repair.notes = (repair.notes or "") + f"\n[{datetime.now(timezone.utc)}] {notes}"

        # Créer un enregistrement dans l'historique des statuts
        status_history = RepairStatusHistory(
            repair_id=repair_id,
            status=new_status,
            changed_at=changed_at or datetime.now(timezone.utc),
            changed_by=changed_by,
            notes=notes
        )

        self.db.add(status_history)
        self.db.commit()

        return True

    async def update_repair_status(
        self,
        repair_id: int,
        new_status: RepairStatus,
        notes: Optional[str] = None,
        changed_at: Optional[datetime] = None,
        changed_by: Optional[int] = None
    ) -> bool:
        """
        Met à jour le statut d'une réparation et enregistre l'historique.
        Alias de update_status pour une meilleure lisibilité.
        """
        return await self.update_status(
            repair_id=repair_id,
            new_status=new_status,
            notes=notes,
            changed_by=changed_by,
            changed_at=changed_at
        )

    async def get_repair_status_history(self, repair_id: int) -> List[RepairStatusHistoryPydantic]:
        """
        Récupère l'historique des statuts d'une réparation.

        Args:
            repair_id: ID de la réparation

        Returns:
            Liste des enregistrements d'historique de statut
        """
        history = (
            self.db.query(RepairStatusHistory)
            .filter(RepairStatusHistory.repair_id == repair_id)
            .order_by(RepairStatusHistory.changed_at.desc())
            .all()
        )

        return [RepairStatusHistoryPydantic.from_orm(item) for item in history]

    async def get_repairs_by_status(
        self,
        status: RepairStatus,
        skip: int = 0,
        limit: int = 50
    ) -> List[RepairOrder]:
        return (
            self.db.query(self.model)
            .filter(self.model.status == status)
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def use_part(
        self,
        repair_id: int,
        part_id: int,
        quantity: int,
        user_id: int
    ) -> bool:
        """Ajoute une pièce à une réparation et ajuste le stock"""
        from ..models.repair import UsedPart

        repair = await self.get(repair_id)
        if not repair:
            return False

        # Ajuster le stock via le service d'inventaire
        success = await self.inventory_service.adjust_stock(
            item_id=part_id,
            quantity_change=-quantity,
            user_id=user_id,
            reference=f"REPAIR-{repair.number}",
            notes=f"Used in repair order #{repair.number}"
        )

        if success:
            # Récupérer la pièce d'inventaire
            part = await self.inventory_service.get(part_id)
            if not part:
                return False

            # Vérifier si la pièce est déjà utilisée dans cette réparation
            existing_part = self.db.query(UsedPart).filter(
                UsedPart.repair_order_id == repair_id,
                UsedPart.part_id == part_id
            ).first()

            if existing_part:
                # Mettre à jour la quantité et le prix total
                existing_part.quantity += quantity
                existing_part.total_price = existing_part.quantity * existing_part.unit_price
                self.db.add(existing_part)
            else:
                # Créer une nouvelle entrée pour la pièce utilisée
                # Convertir en Decimal pour éviter les erreurs d'arrondi
                from decimal import Decimal
                unit_price = Decimal(str(part.selling_price)) if hasattr(part, 'selling_price') else Decimal("0.00")
                db_part = UsedPart(
                    repair_order_id=repair_id,
                    part_id=part_id,
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=Decimal(str(quantity)) * unit_price
                )
                self.db.add(db_part)

            # Mettre à jour le coût des pièces (Decimal)
            from decimal import Decimal
            if hasattr(part, 'selling_price'):
                repair.parts_cost = (Decimal(str(repair.parts_cost)) if repair.parts_cost is not None else Decimal("0.00")) + Decimal(str(part.selling_price)) * Decimal(str(quantity))
            else:
                repair.parts_cost = Decimal(str(repair.parts_cost)) if repair.parts_cost is not None else Decimal("0.00")

            # La main d'œuvre = max(0, total_cost - parts_cost)
            repair.labor_cost = max(Decimal("0.00"), Decimal(str(repair.total_cost)) - Decimal(str(repair.parts_cost)))

            self.db.commit()

        return success

    async def update_used_parts(self, repair_id: int, used_parts: list) -> bool:
        """Met à jour les pièces utilisées pour une réparation"""
        from ..models.repair import UsedPart

        repair = await self.get(repair_id)
        if not repair:
            return False

        try:
            # Récupérer les anciennes pièces utilisées pour restaurer le stock
            old_used_parts = self.db.query(UsedPart).filter(UsedPart.repair_order_id == repair_id).all()

            # Restaurer le stock des anciennes pièces
            for old_part in old_used_parts:
                try:
                    # Récupérer la pièce d'inventaire
                    inventory_item = await self.inventory_service.get(old_part.part_id)
                    if inventory_item:
                        # Restaurer la quantité en stock
                        inventory_item.quantity += old_part.quantity
                        self.db.add(inventory_item)
                except Exception as e:
                    print(f"Erreur lors de la restauration du stock pour la pièce {old_part.part_id}: {e}")

            # Supprimer les anciennes pièces utilisées
            self.db.query(UsedPart).filter(UsedPart.repair_order_id == repair_id).delete()

            # Calculer le coût total des pièces
            parts_cost = 0.0
            for part in used_parts:
                # Créer une nouvelle entrée pour chaque pièce
                db_part = UsedPart(
                    repair_order_id=repair_id,  # Utiliser repair_order_id au lieu de repair_id
                    part_id=part.part_id,
                    quantity=part.quantity,
                    unit_price=part.unit_price,
                    total_price=part.quantity * part.unit_price  # Calculer le prix total
                )
                self.db.add(db_part)

                # Ajouter au coût total des pièces
                parts_cost += part.quantity * part.unit_price

                try:
                    # Mettre à jour le stock
                    inventory_item = await self.inventory_service.get(part.part_id)
                    if inventory_item:
                        # Vérifier si la quantité est suffisante
                        if inventory_item.quantity < part.quantity:
                            print(f"Quantité insuffisante en stock pour la pièce {part.part_id}. Disponible: {inventory_item.quantity}, Demandé: {part.quantity}")
                            continue

                        # Réduire la quantité en stock
                        inventory_item.quantity -= part.quantity
                        self.db.add(inventory_item)
                except Exception as e:
                    print(f"Erreur lors de la mise à jour du stock pour la pièce {part.part_id}: {e}")

            # Mettre à jour le coût des pièces dans la réparation
            repair.parts_cost = parts_cost

            # Le total_cost reste inchangé car c'est le prix final défini par l'utilisateur
            # La main d'œuvre est recalculée comme la différence entre le prix final et le coût des pièces
            repair.labor_cost = max(0.0, repair.total_cost - repair.parts_cost)

            # S'assurer que les modifications sont bien enregistrées dans la base de données
            try:
                # Enregistrer les modifications
                self.db.commit()

                # Vérifier que les modifications ont bien été enregistrées
                import logging
                logger = logging.getLogger(__name__)
                logger.info("Modifications enregistrées dans la base de données")

                # Vérifier que le stock a bien été mis à jour
                for part in used_parts:
                    inventory_item = self.db.query(self.inventory_service.model).filter(self.inventory_service.model.id == part.part_id).first()
                    if inventory_item:
                        logger.info(f"Stock de la pièce {inventory_item.sku} mis à jour: {inventory_item.quantity}")

                # Rafraîchir la session pour s'assurer que les modifications sont bien visibles
                self.db.flush()
                self.db.refresh(repair)

                logger.info("Session rafraîchie")
            except Exception as e:
                logger.error(f"Erreur lors de l'enregistrement des modifications: {e}")
                import traceback
                traceback.print_exc()
                raise

            return True

        except Exception as e:
            self.db.rollback()
            print(f"Erreur lors de la mise à jour des pièces utilisées: {e}")
            return False

    async def get_repair_parts(self, repair_id: int) -> List[Dict[str, Any]]:
        """
        Récupère les pièces utilisées dans une réparation.

        Args:
            repair_id: ID de la réparation

        Returns:
            Liste des pièces utilisées avec leurs détails
        """
        from ..models.repair import UsedPart

        # Récupérer les pièces utilisées
        used_parts = self.db.query(UsedPart).filter(UsedPart.repair_order_id == repair_id).all()

        # Récupérer les détails des pièces
        parts_details = []
        for part in used_parts:
            inventory_item = await self.inventory_service.get(part.part_id)
            if inventory_item:
                parts_details.append({
                    "id": part.id,
                    "part_id": part.part_id,
                    "product_name": inventory_item.name,
                    "sku": inventory_item.sku,
                    "quantity": part.quantity,
                    "unit_price": part.unit_price,
                    "total_price": part.total_price,
                    "lot_number": part.lot_number,
                    "serial_number": part.serial_number,
                    "warranty_period": part.warranty_period
                })

        return parts_details

    async def get_repair_payments(self, repair_id: int) -> List[RepairPaymentPydantic]:
        """Retourne la liste des paiements d'une réparation (ordonnés par date)."""
        payments = (
            self.db.query(RepairPayment)
            .filter(RepairPayment.repair_order_id == repair_id)
            .order_by(RepairPayment.payment_date.asc())
            .all()
        )
        # Convertir en Pydantic pour l'UI
        return [RepairPaymentPydantic.from_orm(p) for p in payments]

    async def record_payment(self, repair_id: int, data: Dict[str, Any]) -> RepairOrder:
        """Enregistre un paiement et met à jour le statut de paiement de la réparation."""
        from decimal import Decimal
        from datetime import timedelta

        # Récupérer la réparation
        repair = await self.get(repair_id)
        if not repair:
            raise ValueError("Repair order not found")

        # Valider le montant
        if "amount" not in data:
            raise ValueError("'amount' is required")
        amount = Decimal(str(data["amount"]))
        if amount <= Decimal("0"):
            raise ValueError("Amount must be > 0")

        # Normaliser la méthode de paiement
        pm = data.get("payment_method")
        if isinstance(pm, str):
            try:
                pm = PaymentMethod(pm)
            except Exception:
                pm = PaymentMethod.cash

        # Créer le paiement
        payment = RepairPayment(
            repair_order_id=repair_id,
            amount=amount,
            payment_method=pm,
            payment_date=data.get("payment_date", datetime.now(timezone.utc)),
            reference_number=data.get("reference_number"),
            notes=data.get("notes"),
            processed_by=data.get("processed_by"),
        )
        self.db.add(payment)

        # Mettre à jour les totaux
        current_total_paid = Decimal(str(repair.total_paid)) if getattr(repair, "total_paid", None) is not None else Decimal("0.00")
        final_amount = Decimal(str(repair.final_amount)) if getattr(repair, "final_amount", None) is not None else Decimal("0.00")
        new_total_paid = current_total_paid + amount
        repair.total_paid = new_total_paid

        # Statut de paiement
        if final_amount > Decimal("0.00") and new_total_paid >= final_amount:
            repair.payment_status = PaymentStatus.PAID
            # Marquer la réparation comme terminée si entièrement payée
            if repair.status != RepairStatus.COMPLETED:
                repair.status = RepairStatus.COMPLETED
            repair.payment_date = payment.payment_date
        elif new_total_paid > Decimal("0.00"):
            repair.payment_status = PaymentStatus.PARTIAL
        else:
            repair.payment_status = PaymentStatus.PENDING

        # Gestion du crédit: renseigner l'échéance si nécessaire
        if pm == PaymentMethod.credit:
            # Essayer d'obtenir des conditions depuis les données ou le client
            credit_terms = data.get("credit_terms")
            if credit_terms is None and getattr(repair, "customer_id", None):
                try:
                    customer_service = CustomerService(self.db)
                    customer = await customer_service.get(repair.customer_id)
                    if customer and hasattr(customer, "default_payment_terms"):
                        credit_terms = customer.default_payment_terms
                except Exception:
                    credit_terms = None
            if credit_terms is not None:
                try:
                    repair.credit_terms = int(credit_terms)
                    base_dt = payment.payment_date or datetime.now(timezone.utc)
                    repair.due_date = base_dt + timedelta(days=repair.credit_terms)
                except Exception:
                    pass

        # Enregistrer une transaction de caisse pour les paiements encaissés (hors crédit)
        try:
            if pm != PaymentMethod.credit:
                # Si un ID de caisse est fourni, l'utiliser en priorité
                register = None
                provided_register_id = data.get("cash_register_id")
                if provided_register_id:
                    register = self.db.query(CashRegister).get(provided_register_id)
                    if not register or not getattr(register, "is_active", True):
                        register = None  # Fallback si invalide

                # Sinon: utiliser la caisse par défaut si configurée
                if not register:
                    try:
                        from config.settings import get_settings
                        settings = get_settings()
                        default_id = getattr(settings, "default_repair_cash_register_id", None)
                        if default_id:
                            register = self.db.query(CashRegister).get(default_id)
                            if not register or not getattr(register, "is_active", True):
                                register = None
                    except Exception:
                        register = None

                # Sinon: trouver automatiquement une caisse active (REPAIR > MAIN > any)
                if not register:
                    register = (
                        self.db.query(CashRegister)
                        .filter(CashRegister.is_active == True, CashRegister.type == CashRegisterType.REPAIR)
                        .first()
                    ) or (
                        self.db.query(CashRegister)
                        .filter(CashRegister.is_active == True, CashRegister.type == CashRegisterType.MAIN)
                        .first()
                    ) or (
                        self.db.query(CashRegister)
                        .filter(CashRegister.is_active == True)
                        .first()
                    )

                processed_by = data.get("processed_by")
                if register and processed_by:
                    # Mapper la méthode de paiement vers la trésorerie
                    try:
                        treas_method = TreasuryPaymentMethod(pm.value)
                    except Exception:
                        treas_method = TreasuryPaymentMethod.other

                    await self.treasury_service.add_transaction({
                        "cash_register_id": register.id,
                        "amount": float(amount),
                        "transaction_date": payment.payment_date,
                        "category": TransactionCategory.REPAIR,
                        "payment_method": treas_method,
                        "reference_number": data.get("reference_number"),
                        "description": f"Paiement réparation #{repair.number}",
                        "sale_id": None,
                        "repair_id": repair_id,
                        "purchase_id": None,
                        "supplier_payment_id": None,
                        "customer_transaction_id": None,
                        "expense_id": None,
                        "user_id": processed_by,
                    })
        except Exception as te:
            # Ne pas bloquer l'enregistrement du paiement si la trésorerie échoue
            print(f"Erreur lors de la création de la transaction de caisse: {te}")

        # Sauvegarder
        self.db.commit()
        self.db.refresh(repair)
        return repair

    async def add_repair_part(
        self,
        repair_id: int,
        product_id: int,
        quantity: int,
        unit_price: float,
        lot_number: Optional[str] = None,
        serial_number: Optional[str] = None,
        deduct_stock: bool = True
    ) -> bool:
        """
        Ajoute une pièce à une réparation.

        Args:
            repair_id: ID de la réparation
            product_id: ID du produit
            quantity: Quantité
            unit_price: Prix unitaire
            lot_number: Numéro de lot
            serial_number: Numéro de série
            deduct_stock: Si True, déduit la quantité du stock

        Returns:
            True si l'ajout a réussi, False sinon
        """
        from ..models.repair import UsedPart

        repair = await self.get(repair_id)
        if not repair:
            return False

        # Récupérer le produit
        product = await self.inventory_service.get(product_id)
        if not product:
            return False

        # Vérifier si la pièce est déjà utilisée dans cette réparation
        existing_part = self.db.query(UsedPart).filter(
            UsedPart.repair_order_id == repair_id,
            UsedPart.part_id == product_id
        ).first()

        if existing_part:
            # Mettre à jour la quantité et le prix total
            existing_part.quantity += quantity
            existing_part.total_price = existing_part.quantity * unit_price
            self.db.add(existing_part)
        else:
            # Créer une nouvelle entrée pour la pièce utilisée
            total_price = quantity * unit_price
            db_part = UsedPart(
                repair_order_id=repair_id,
                part_id=product_id,
                quantity=quantity,
                unit_price=unit_price,
                total_price=total_price,
                lot_number=lot_number,
                serial_number=serial_number,
                warranty_period=getattr(product, 'warranty_period', None)
            )
            self.db.add(db_part)

        # Mettre à jour le coût des pièces
        repair.parts_cost += quantity * unit_price

        # Déduire du stock si demandé
        if deduct_stock:
            await self.inventory_service.adjust_stock(
                item_id=product_id,
                quantity_change=-quantity,
                reference=f"REPAIR-{repair.number}",
                notes=f"Utilisé dans la réparation #{repair.number}"
            )

        self.db.commit()
        return True

    async def delete_repair_part(self, part_id: int, restore_stock: bool = True) -> bool:
        """
        Supprime une pièce d'une réparation.

        Args:
            part_id: ID de la pièce utilisée
            restore_stock: Si True, restaure la quantité dans le stock

        Returns:
            True si la suppression a réussi, False sinon
        """
        from ..models.repair import UsedPart

        # Récupérer la pièce utilisée
        used_part = self.db.query(UsedPart).filter(UsedPart.id == part_id).first()
        if not used_part:
            return False

        # Récupérer la réparation
        repair = await self.get(used_part.repair_order_id)
        if not repair:
            return False

        # Mettre à jour le coût des pièces
        repair.parts_cost -= used_part.total_price

        # Restaurer le stock si demandé
        if restore_stock:
            await self.inventory_service.adjust_stock(
                item_id=used_part.part_id,
                quantity_change=used_part.quantity,
                reference=f"REPAIR-{repair.number}",
                notes=f"Restauré du stock suite à la suppression d'une pièce de la réparation #{repair.number}"
            )

        # Supprimer la pièce utilisée
        self.db.delete(used_part)
        self.db.commit()

        return True

    async def get_repair(self, repair_id: int) -> Optional[RepairOrder]:
        """
        Récupère une réparation par son ID.

        Args:
            repair_id: ID de la réparation

        Returns:
            La réparation ou None si elle n'existe pas
        """
        return await self.get(repair_id)

    async def get_repair_with_details(self, repair_id: int) -> Dict[str, Any]:
        """Récupère une réparation avec tous ses détails pour l'impression"""
        from ..models.repair import UsedPart

        repair = await self.get(repair_id)
        if not repair:
            return {}

        # Initialiser le service client
        customer_service = CustomerService(self.db)

        # Récupérer le client
        customer = await customer_service.get(repair.customer_id) if repair.customer_id else None

        # Récupérer les pièces utilisées
        used_parts = self.db.query(UsedPart).filter(UsedPart.repair_order_id == repair_id).all()

        # Récupérer les détails des pièces
        parts_details = []
        for part in used_parts:
            inventory_item = await self.inventory_service.get(part.part_id)
            if inventory_item:
                parts_details.append({
                    "id": part.id,
                    "part_id": part.part_id,
                    "sku": inventory_item.sku,
                    "name": inventory_item.name,
                    "quantity": part.quantity,
                    "unit_price": part.unit_price,
                    "total_price": part.total_price
                })

        # Construire le dictionnaire de données
        repair_data = {
            "id": repair.id,
            "number": repair.number,
            "created_at": repair.created_at,
            "updated_at": repair.updated_at,
            "status": repair.status.value if hasattr(repair.status, 'value') else repair.status,
            "brand": repair.brand,
            "model": repair.model,
            "serial_number": repair.serial_number,
            "description": repair.description,
            # Utiliser getattr pour éviter l'erreur si l'attribut diagnosis n'existe pas
            "diagnosis": getattr(repair, 'diagnosis', None) or getattr(repair, 'notes', 'Aucun diagnostic fourni'),
            "work_performed": getattr(repair, 'work_performed', None) or getattr(repair, 'description', 'Aucun travail spécifié'),
            "parts_cost": repair.parts_cost,
            "labor_cost": repair.labor_cost,
            "total_cost": repair.total_cost,
            "estimated_price": getattr(repair, 'estimated_price', repair.total_cost),
            "estimated_completion_date": getattr(repair, 'estimated_completion_date', None),
            "completion_date": repair.completion_date,
            "notes": repair.notes,
            "customer_id": repair.customer_id,
            "technician_id": repair.technician_id,
            "tax_amount": repair.tax_amount,
            "discount_amount": repair.discount_amount,
            "final_amount": repair.final_amount,
            "payment_status": repair.payment_status,
            "payment_method": repair.payment_method,
            "payment_date": repair.payment_date,
            "invoice_number": repair.invoice_number,
            "invoice_date": repair.invoice_date,
            "credit_terms": repair.credit_terms,
            "due_date": repair.due_date,
            "total_paid": repair.total_paid,
            "customer": {
                "id": customer.id,
                "name": customer.name,
                "phone": customer.phone,
                "email": customer.email,
                "address": customer.address
            } if customer else {},
            "used_parts": parts_details
        }

        return repair_data

    async def print_repair_order(self, repair_id: int, output_path: str = None) -> str:
        """Génère un PDF d'ordre de réparation"""
        print(f"Génération de l'ordre de réparation pour la réparation #{repair_id}")
        repair_data = await self.get_repair_with_details(repair_id)
        if not repair_data:
            print(f"Erreur: Aucune donnée trouvée pour la réparation #{repair_id}")
            return None

        print(f"Données de réparation récupérées: {repair_data.get('id')}, {repair_data.get('number')}")

        # Générer un nom de fichier par défaut si non spécifié
        if not output_path:
            output_dir = os.path.join(os.getcwd(), "output")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"repair_order_{repair_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf")
            print(f"Chemin de sortie généré: {output_path}")

        # Générer le PDF
        print(f"Création du générateur de PDF avec le chemin: {output_path}")
        pdf_generator = RepairPDFGenerator(output_path)
        result = pdf_generator.generate_repair_order(repair_data)
        print(f"Résultat de la génération du PDF: {result}")
        return result

    async def print_repair_invoice(self, repair_id: int, output_path: str = None) -> str:
        """Génère un PDF de facture de réparation"""
        print(f"Génération de la facture pour la réparation #{repair_id}")
        repair_data = await self.get_repair_with_details(repair_id)
        if not repair_data:
            print(f"Erreur: Aucune donnée trouvée pour la réparation #{repair_id}")
            return None

        print(f"Données de réparation récupérées: {repair_data.get('id')}, {repair_data.get('number')}")

        # Générer un nom de fichier par défaut si non spécifié
        if not output_path:
            output_dir = os.path.join(os.getcwd(), "output")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"repair_invoice_{repair_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf")
            print(f"Chemin de sortie généré: {output_path}")

        # Générer le PDF
        print(f"Création du générateur de PDF avec le chemin: {output_path}")
        pdf_generator = RepairPDFGenerator(output_path)
        result = pdf_generator.generate_repair_invoice(repair_data)
        print(f"Résultat de la génération du PDF: {result}")
        return result

    async def get_repair_payments(self, repair_id: int) -> List[RepairPaymentPydantic]:
        """
        Récupère la liste des paiements d'une réparation.
        """
        try:
            payments = (
                self.db.query(RepairPayment)
                .filter(RepairPayment.repair_order_id == repair_id)
                .order_by(RepairPayment.payment_date.desc(), RepairPayment.id.desc())
                .all()
            )
            return [RepairPaymentPydantic.from_orm(p) for p in payments]
        except Exception as e:
            print(f"Erreur lors de la récupération des paiements de la réparation {repair_id}: {e}")
            return []

    async def create_invoice(
        self,
        repair_id: int,
        invoice_number: Optional[str] = None,
        invoice_date: Optional[datetime] = None,
        recalc_final_amount: bool = True,
        update_status: bool = True
    ) -> RepairOrder:
        """
        Crée/attribue un numéro de facture et une date à la réparation.
        Optionnellement recalcule le montant final et met à jour le statut en INVOICED.
        """
        repair = await self.get(repair_id)
        if not repair:
            raise ValueError(f"Réparation avec ID {repair_id} non trouvée")

        # Définir numéro de facture
        if not invoice_number:
            now = datetime.now(timezone.utc)
            base_ref = repair.number if getattr(repair, 'number', None) else str(repair_id)
            invoice_number = f"INV-{now.strftime('%Y%m%d')}-{base_ref}"

        repair.invoice_number = invoice_number
        repair.invoice_date = invoice_date or datetime.now(timezone.utc)

        # Recalculer le montant final si demandé (et si non défini)
        if recalc_final_amount and (not repair.final_amount or float(repair.final_amount) == 0.0):
            from decimal import Decimal
            parts_cost = Decimal(str(repair.parts_cost or 0))
            labor_cost = Decimal(str(repair.labor_cost or 0))
            tax_amount = Decimal(str(repair.tax_amount or 0))
            discount_amount = Decimal(str(repair.discount_amount or 0))
            repair.final_amount = parts_cost + labor_cost + tax_amount - discount_amount

        # Mettre à jour le statut si non payé (garder le statut actuel ou passer à terminé si pas encore fait)
        if update_status and getattr(repair, 'payment_status', None) != PaymentStatus.PAID:
            if repair.status not in [RepairStatus.COMPLETED, RepairStatus.CANCELLED]:
                repair.status = RepairStatus.COMPLETED

        self.db.commit()
        self.db.refresh(repair)
        return repair

    async def generate_and_print_invoice(
        self,
        repair_id: int,
        invoice_number: Optional[str] = None,
        output_path: Optional[str] = None
    ) -> Optional[str]:
        """
        Génère (si besoin) la facture de la réparation et imprime le PDF.
        """
        await self.create_invoice(repair_id, invoice_number=invoice_number)
        return await self.print_repair_invoice(repair_id, output_path)

    async def record_payment(self, repair_id: int, payment_data: dict) -> RepairOrder:
        """
        Enregistre un paiement pour une réparation et l'ajoute à la caisse des réparations

        Args:
            repair_id: ID de la réparation
            payment_data: Données du paiement contenant:
                - amount: Montant du paiement
                - payment_method: Méthode de paiement
                - payment_date: Date du paiement (optionnel)
                - reference_number: Référence du paiement (optionnel)
                - notes: Notes supplémentaires (optionnel)
                - processed_by: ID de l'utilisateur qui a traité le paiement (optionnel)
                - send_receipt: Indique si un reçu doit être envoyé (optionnel)

        Returns:
            La réparation mise à jour
        """
        # Récupérer la réparation
        repair = await self.get(repair_id)
        if not repair:
            raise ValueError(f"Réparation avec ID {repair_id} non trouvée")

        # Créer l'enregistrement de paiement
        payment = RepairPayment(
            repair_order_id=repair_id,
            amount=payment_data['amount'],
            payment_method=payment_data['payment_method'],
            payment_date=payment_data.get('payment_date', datetime.now(timezone.utc)),
            reference_number=payment_data.get('reference_number'),
            notes=payment_data.get('notes'),
            processed_by=payment_data.get('processed_by')
        )

        # Ajouter le paiement à la base de données
        self.db.add(payment)
        self.db.flush()  # Pour obtenir l'ID du paiement

        # Mettre à jour le montant total payé et le statut de paiement
        repair.total_paid = (repair.total_paid or 0) + payment_data['amount']

        # Déterminer le statut de paiement
        if repair.total_paid >= repair.final_amount:
            repair.payment_status = PaymentStatus.PAID
            # Marquer comme terminé si entièrement payé
            if repair.status != RepairStatus.COMPLETED:
                repair.status = RepairStatus.COMPLETED
            repair.payment_date = datetime.now(timezone.utc)
        elif repair.total_paid > 0:
            repair.payment_status = PaymentStatus.PARTIAL

        # Enregistrer le paiement dans la caisse des réparations
        try:
            # Trouver la caisse des réparations
            repair_cash_register = self.db.query(CashRegister).filter(
                CashRegister.type == CashRegisterType.REPAIR,
                CashRegister.is_active == True
            ).first()

            # Ne créer une transaction de caisse que pour les paiements non-crédit
            should_create_cash_tx = True
            try:
                # payment_method peut être un enum ou une chaîne
                pm = payment_data.get('payment_method')
                if pm == 'credit' or (hasattr(pm, 'value') and pm.value == 'credit'):
                    should_create_cash_tx = False
            except Exception:
                pass

            if not repair_cash_register:
                # Créer automatiquement une caisse de type REPAIR si absente
                try:
                    created = await self.treasury_service.create_cash_register({
                        "name": "Caisse Réparations",
                        "type": CashRegisterType.REPAIR,
                        "initial_balance": 0.0,
                        "notes": "Créée automatiquement pour les paiements de réparations"
                    })
                    repair_cash_register = created
                except Exception as create_err:
                    print(f"Impossible de créer la caisse Réparations: {create_err}")

            if repair_cash_register and should_create_cash_tx:
                # Mapper la méthode de paiement vers la trésorerie
                treasury_method = None
                try:
                    pm = payment_data.get('payment_method')
                    if hasattr(pm, 'value'):
                        treasury_method = TreasuryPaymentMethod(pm.value)
                    elif isinstance(pm, str):
                        treasury_method = TreasuryPaymentMethod(pm)
                except Exception:
                    treasury_method = TreasuryPaymentMethod.other

                # Créer une transaction dans la caisse
                transaction_data = {
                    "cash_register_id": repair_cash_register.id,
                    "amount": payment_data['amount'],
                    "transaction_date": payment_data.get('payment_date', datetime.now(timezone.utc)),
                    "category": TransactionCategory.REPAIR,
                    "payment_method": treasury_method,
                    "reference_number": payment_data.get('reference_number'),
                    "description": f"Paiement pour réparation #{repair.number}",
                    "repair_id": repair_id,
                    "user_id": payment_data.get('processed_by', 1)  # Utiliser l'ID 1 par défaut si non spécifié
                }

                # Ajouter la transaction à la caisse
                await self.treasury_service.add_transaction(transaction_data)

                # Notifier la mise à jour de la trésorerie
                try:
                    from app.utils.treasury_updater import notify_repair_payment
                    notify_repair_payment(repair_id, payment_data['amount'], repair_cash_register.id)
                except Exception as notify_error:
                    print(f"Erreur lors de la notification de mise à jour de la trésorerie: {notify_error}")
            elif not should_create_cash_tx:
                print("Paiement à crédit: aucune transaction de caisse créée.")
            else:
                print("Aucune caisse Réparations disponible et création automatique impossible; transaction non créée.")

        except Exception as e:
            # En cas d'erreur, enregistrer l'erreur mais continuer
            print(f"Erreur lors de l'enregistrement du paiement dans la caisse: {e}")
            import traceback
            traceback.print_exc()

        # Mettre à jour la réparation
        self.db.commit()

        # Envoyer un reçu au client si demandé
        if payment_data.get('send_receipt', True) and repair.customer_id:
            try:
                receipt_path = await self.print_repair_receipt(repair_id, payment.id)
                # Ici, vous pourriez implémenter l'envoi du reçu par email ou autre
            except Exception as e:
                print(f"Erreur lors de la génération du reçu: {e}")

        return repair

    async def print_repair_receipt(self, repair_id: int, payment_id: int, output_path: str = None) -> str:
        """Génère un PDF de reçu de paiement pour une réparation"""
        from ..models.repair import RepairPayment

        print(f"Génération du reçu pour la réparation #{repair_id}, paiement #{payment_id}")
        repair_data = await self.get_repair_with_details(repair_id)
        if not repair_data:
            print(f"Erreur: Aucune donnée trouvée pour la réparation #{repair_id}")
            return None

        print(f"Données de réparation récupérées: {repair_data.get('id')}, {repair_data.get('number')}")

        # Récupérer les détails du paiement
        payment = self.db.query(RepairPayment).filter(RepairPayment.id == payment_id).first()
        if not payment:
            print(f"Erreur: Aucun paiement trouvé avec l'ID {payment_id}")
            return None

        print(f"Données de paiement récupérées: {payment.id}, {payment.amount}")

        payment_data = {
            "id": payment.id,
            "repair_order_id": payment.repair_order_id,
            "amount": payment.amount,
            "payment_method": payment.payment_method,
            "payment_date": payment.payment_date,
            "reference": payment.reference_number,
            "notes": payment.notes
        }

        # Générer un nom de fichier par défaut si non spécifié
        if not output_path:
            output_dir = os.path.join(os.getcwd(), "output")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"repair_receipt_{repair_id}_{payment_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf")
            print(f"Chemin de sortie généré: {output_path}")

        # Générer le PDF
        print(f"Création du générateur de PDF avec le chemin: {output_path}")
        pdf_generator = RepairPDFGenerator(output_path)
        result = pdf_generator.generate_repair_receipt(repair_data, payment_data)
        print(f"Résultat de la génération du PDF: {result}")
        return result

    async def print_repair_deposit_receipt(self, repair_id: int, output_path: str = None) -> str:
        """Génère un PDF de reçu de dépôt pour une réparation"""
        print(f"Génération du reçu de dépôt pour la réparation #{repair_id}")
        repair_data = await self.get_repair_with_details(repair_id)
        if not repair_data:
            print(f"Erreur: Aucune donnée trouvée pour la réparation #{repair_id}")
            return None

        print(f"Données de réparation récupérées: {repair_data.get('id')}, {repair_data.get('number')}")

        # Générer un nom de fichier par défaut si non spécifié
        if not output_path:
            output_dir = os.path.join(os.getcwd(), "output")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"repair_deposit_receipt_{repair_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf")
            print(f"Chemin de sortie généré: {output_path}")

        # Générer le PDF
        print(f"Création du générateur de PDF avec le chemin: {output_path}")
        pdf_generator = RepairPDFGenerator(output_path)
        result = pdf_generator.generate_repair_deposit_receipt(repair_data)
        print(f"Résultat de la génération du PDF: {result}")
        return result

    async def print_completed_repair_receipt(self, repair_id: int, output_path: str = None) -> str:
        """
        Génère un PDF de reçu pour une réparation terminée.

        Args:
            repair_id: ID de la réparation
            output_path: Chemin de sortie du fichier PDF (optionnel)

        Returns:
            Le chemin du fichier PDF généré ou None si la génération a échoué
        """
        print(f"Génération du reçu de réparation terminée pour la réparation #{repair_id}")
        repair_data = await self.get_repair_with_details(repair_id)
        if not repair_data:
            print(f"Erreur: Aucune donnée trouvée pour la réparation #{repair_id}")
            return None

        print(f"Données de réparation récupérées: {repair_data.get('id')}, {repair_data.get('number')}")

        # Vérifier que la réparation est terminée
        status = repair_data.get('status')
        if status not in ['COMPLETED', 'INVOICED', 'PAID']:
            print(f"Erreur: La réparation #{repair_id} n'est pas terminée (statut: {status})")
            return None

        # Générer un nom de fichier par défaut si non spécifié
        if not output_path:
            output_dir = os.path.join(os.getcwd(), "output")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"repair_receipt_{repair_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf")
            print(f"Chemin de sortie généré: {output_path}")

        # Générer le PDF
        print(f"Création du générateur de PDF avec le chemin: {output_path}")
        pdf_generator = RepairPDFGenerator(output_path)
        result = pdf_generator.generate_completed_repair_receipt(repair_data)
        print(f"Résultat de la génération du PDF: {result}")
        return result

    async def add_photo(self, photo_data: RepairPhotoPydantic) -> Optional[RepairPhotoPydantic]:
        """
        Ajoute une photo à une réparation.

        Args:
            photo_data: Données de la photo à ajouter

        Returns:
            Les données de la photo ajoutée ou None en cas d'erreur
        """
        try:
            # Vérifier que la réparation existe
            repair = await self.get(photo_data.repair_id)
            if not repair:
                return None

            # Créer l'entrée dans la base de données
            db_photo = RepairPhoto(
                repair_id=photo_data.repair_id,
                file_path=photo_data.file_path,
                thumbnail_path=photo_data.thumbnail_path,
                photo_type=photo_data.photo_type,
                title=photo_data.title,
                description=photo_data.description,
                taken_at=photo_data.taken_at,
                taken_by=photo_data.taken_by
            )

            self.db.add(db_photo)
            self.db.commit()
            self.db.refresh(db_photo)

            return RepairPhotoPydantic.from_orm(db_photo)

        except Exception as e:
            self.db.rollback()
            print(f"Erreur lors de l'ajout de la photo: {e}")
            return None

    async def delete_photo(self, photo_id: int) -> bool:
        """
        Supprime une photo de réparation.

        Args:
            photo_id: ID de la photo à supprimer

        Returns:
            True si la suppression a réussi, False sinon
        """
        try:
            # Récupérer la photo
            photo = self.db.query(RepairPhoto).filter(RepairPhoto.id == photo_id).first()
            if not photo:
                return False

            # Supprimer le fichier physique
            if os.path.exists(photo.file_path):
                os.remove(photo.file_path)

            # Supprimer la miniature si elle existe
            if photo.thumbnail_path and os.path.exists(photo.thumbnail_path):
                os.remove(photo.thumbnail_path)

            # Supprimer l'entrée de la base de données
            self.db.delete(photo)
            self.db.commit()

            return True

        except Exception as e:
            self.db.rollback()
            print(f"Erreur lors de la suppression de la photo: {e}")
            return False

    async def get_repair_photos(self, repair_id: int) -> List[RepairPhotoPydantic]:
        """
        Récupère toutes les photos d'une réparation.

        Args:
            repair_id: ID de la réparation

        Returns:
            Liste des photos de la réparation
        """
        try:
            photos = (
                self.db.query(RepairPhoto)
                .filter(RepairPhoto.repair_id == repair_id)
                .order_by(RepairPhoto.taken_at.desc())
                .all()
            )

            return [RepairPhotoPydantic.from_orm(photo) for photo in photos]

        except Exception as e:
            print(f"Erreur lors de la récupération des photos: {e}")
            return []

    async def update_photo(self, photo_id: int, photo_data: RepairPhotoPydantic) -> Optional[RepairPhotoPydantic]:
        """
        Met à jour les métadonnées d'une photo.

        Args:
            photo_id: ID de la photo à mettre à jour
            photo_data: Nouvelles données de la photo

        Returns:
            Les données de la photo mise à jour ou None en cas d'erreur
        """
        try:
            # Récupérer la photo
            photo = self.db.query(RepairPhoto).filter(RepairPhoto.id == photo_id).first()
            if not photo:
                return None

            # Mettre à jour les champs
            photo.photo_type = photo_data.photo_type
            photo.title = photo_data.title
            photo.description = photo_data.description

            self.db.commit()
            self.db.refresh(photo)

            return RepairPhotoPydantic.from_orm(photo)

        except Exception as e:
            self.db.rollback()
            print(f"Erreur lors de la mise à jour de la photo: {e}")
            return None

