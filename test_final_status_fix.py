#!/usr/bin/env python3
"""
Test final pour vérifier que la correction des statuts de réparation est complète
"""

import sqlite3
import sys
import os

def test_database_consistency():
    """Teste la cohérence de la base de données"""
    print("=== Test de Cohérence de la Base de Données ===")
    
    db_path = "data/app.db"
    if not os.path.exists(db_path):
        print(f"✗ Base de données non trouvée: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier les statuts dans repair_orders
        cursor.execute("SELECT DISTINCT status FROM repair_orders WHERE status IS NOT NULL")
        repair_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Statuts dans repair_orders: {repair_statuses}")
        
        # Vérifier les statuts dans repair_status_history
        cursor.execute("SELECT DISTINCT status FROM repair_status_history WHERE status IS NOT NULL")
        history_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Statuts dans l'historique: {history_statuses}")
        
        # Statuts valides selon l'énumération database/models.py
        valid_statuses = ['PENDING', 'IN_PROGRESS', 'ON_HOLD', 'COMPLETED', 'CANCELLED']
        
        # Vérifier repair_orders
        invalid_repair_statuses = [s for s in repair_statuses if s not in valid_statuses]
        if invalid_repair_statuses:
            print(f"✗ Statuts invalides dans repair_orders: {invalid_repair_statuses}")
            return False
        else:
            print("✓ Tous les statuts dans repair_orders sont valides")
        
        # Vérifier repair_status_history
        invalid_history_statuses = [s for s in history_statuses if s not in valid_statuses]
        if invalid_history_statuses:
            print(f"✗ Statuts invalides dans l'historique: {invalid_history_statuses}")
            return False
        else:
            print("✓ Tous les statuts dans l'historique sont valides")
        
        # Compter les réparations
        cursor.execute("SELECT COUNT(*) FROM repair_orders")
        total_repairs = cursor.fetchone()[0]
        print(f"Total des réparations: {total_repairs}")
        
        # Répartition par statut
        cursor.execute("SELECT status, COUNT(*) FROM repair_orders GROUP BY status")
        status_counts = cursor.fetchall()
        print("Répartition par statut:")
        for status, count in status_counts:
            print(f"  - {status}: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test de la base de données: {e}")
        return False

def test_enum_import():
    """Teste l'import des énumérations"""
    print("\n=== Test d'Import des Énumérations ===")
    
    try:
        # Test import de l'énumération principale
        from app.core.models.repair import RepairStatus, PaymentStatus
        print("✓ Import de app.core.models.repair réussi")
        
        # Test import de l'énumération de la base de données
        from database.models import RepairStatusEnum
        print("✓ Import de database.models réussi")
        
        # Vérifier les valeurs
        repair_status_values = [s.value for s in RepairStatus]
        repair_status_enum_values = [s.value for s in RepairStatusEnum]
        
        print(f"RepairStatus values: {repair_status_values}")
        print(f"RepairStatusEnum values: {repair_status_enum_values}")
        
        # Les valeurs doivent être cohérentes (une en minuscules, l'autre en majuscules)
        repair_status_upper = [s.upper() for s in repair_status_values]
        if set(repair_status_upper) == set(repair_status_enum_values):
            print("✓ Les énumérations sont cohérentes")
        else:
            print("✗ Les énumérations ne sont pas cohérentes")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test d'import: {e}")
        return False

def test_display_labels():
    """Teste les labels d'affichage"""
    print("\n=== Test des Labels d'Affichage ===")
    
    try:
        from app.ui.utils.display_maps import STATUS_LABELS, PAYMENT_STATUS_LABELS
        from app.core.models.repair import RepairStatus, PaymentStatus
        
        # Vérifier que tous les statuts ont des labels
        missing_labels = []
        for status in RepairStatus:
            if status not in STATUS_LABELS:
                missing_labels.append(status.value)
        
        if missing_labels:
            print(f"✗ Labels manquants pour les statuts: {missing_labels}")
            return False
        else:
            print("✓ Tous les statuts de réparation ont des labels")
        
        # Vérifier les labels de paiement
        missing_payment_labels = []
        for status in PaymentStatus:
            if status not in PAYMENT_STATUS_LABELS:
                missing_payment_labels.append(status.value)
        
        if missing_payment_labels:
            print(f"✗ Labels manquants pour les statuts de paiement: {missing_payment_labels}")
            return False
        else:
            print("✓ Tous les statuts de paiement ont des labels")
        
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test des labels: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🔍 Test Final de la Correction des Statuts")
    print("=" * 50)
    
    tests = [
        ("Cohérence de la base de données", test_database_consistency),
        ("Import des énumérations", test_enum_import),
        ("Labels d'affichage", test_display_labels)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Résultats Finaux")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 SUCCÈS ! Tous les tests sont passés.")
        print("La correction des statuts de réparation est complète et fonctionnelle.")
        print("\nStatuts disponibles:")
        print("- En attente (PENDING)")
        print("- En cours (IN_PROGRESS)")
        print("- En pause (ON_HOLD)")
        print("- Terminé (COMPLETED)")
        print("- Annulé (CANCELLED)")
        print("\nStatut de paiement ajouté:")
        print("- Livrée (DELIVERED)")
    else:
        print("❌ ÉCHEC ! Certains tests ont échoué.")
        print("Vérifiez les erreurs ci-dessus.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
