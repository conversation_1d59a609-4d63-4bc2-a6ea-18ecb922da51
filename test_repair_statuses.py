#!/usr/bin/env python3
"""
Script de test pour vérifier que les nouveaux statuts de réparation fonctionnent correctement
"""

import sys
import os
import sqlite3
from pathlib import Path

# Ajouter le répertoire racine au path
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_enum_values():
    """Teste que les énumérations contiennent les bonnes valeurs"""
    print("=== Test des Énumérations ===")
    
    try:
        from app.core.models.repair import RepairStatus, PaymentStatus
        
        # Test RepairStatus
        expected_repair_statuses = ['pending', 'in_progress', 'on_hold', 'completed', 'cancelled']
        actual_repair_statuses = [status.value for status in RepairStatus]
        
        print(f"Statuts de réparation attendus: {expected_repair_statuses}")
        print(f"Statuts de réparation actuels: {actual_repair_statuses}")
        
        if set(expected_repair_statuses) == set(actual_repair_statuses):
            print("✓ Énumération RepairStatus correcte")
        else:
            print("✗ Énumération RepairStatus incorrecte")
            return False
        
        # Test PaymentStatus
        expected_payment_statuses = ['pending', 'partial', 'paid', 'overdue', 'cancelled', 'delivered']
        actual_payment_statuses = [status.value for status in PaymentStatus]
        
        print(f"Statuts de paiement attendus: {expected_payment_statuses}")
        print(f"Statuts de paiement actuels: {actual_payment_statuses}")
        
        if set(expected_payment_statuses) == set(actual_payment_statuses):
            print("✓ Énumération PaymentStatus correcte")
        else:
            print("✗ Énumération PaymentStatus incorrecte")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test des énumérations: {e}")
        return False

def test_database_values():
    """Teste que les valeurs dans la base de données sont valides"""
    print("\n=== Test des Valeurs en Base de Données ===")
    
    db_path = "data/app.db"
    if not os.path.exists(db_path):
        print(f"✗ Base de données non trouvée: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier les statuts dans repair_orders
        cursor.execute("SELECT DISTINCT status FROM repair_orders WHERE status IS NOT NULL")
        db_statuses = [row[0] for row in cursor.fetchall()]
        
        print(f"Statuts dans la base de données: {db_statuses}")
        
        valid_statuses = ['pending', 'in_progress', 'on_hold', 'completed', 'cancelled']
        invalid_statuses = [status for status in db_statuses if status not in valid_statuses]
        
        if not invalid_statuses:
            print("✓ Tous les statuts en base sont valides")
        else:
            print(f"✗ Statuts invalides trouvés: {invalid_statuses}")
            return False
        
        # Vérifier l'historique des statuts
        cursor.execute("SELECT DISTINCT status FROM repair_status_history WHERE status IS NOT NULL")
        history_statuses = [row[0] for row in cursor.fetchall()]
        
        print(f"Statuts dans l'historique: {history_statuses}")
        
        invalid_history_statuses = [status for status in history_statuses if status not in valid_statuses]
        
        if not invalid_history_statuses:
            print("✓ Tous les statuts dans l'historique sont valides")
        else:
            print(f"✗ Statuts invalides dans l'historique: {invalid_history_statuses}")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test de la base de données: {e}")
        return False

def test_display_labels():
    """Teste que les labels d'affichage sont corrects"""
    print("\n=== Test des Labels d'Affichage ===")
    
    try:
        from app.ui.utils.display_maps import STATUS_LABELS, PAYMENT_STATUS_LABELS
        from app.core.models.repair import RepairStatus, PaymentStatus
        
        # Test des labels de statuts de réparation
        print("Labels de statuts de réparation:")
        for status in RepairStatus:
            label = STATUS_LABELS.get(status, "MANQUANT")
            print(f"  {status.value} -> {label}")
            if label == "MANQUANT":
                print(f"✗ Label manquant pour {status.value}")
                return False
        
        print("✓ Tous les labels de statuts de réparation sont présents")
        
        # Test des labels de statuts de paiement
        print("Labels de statuts de paiement:")
        for status in PaymentStatus:
            label = PAYMENT_STATUS_LABELS.get(status, "MANQUANT")
            print(f"  {status.value} -> {label}")
            if label == "MANQUANT":
                print(f"✗ Label manquant pour {status.value}")
                return False
        
        print("✓ Tous les labels de statuts de paiement sont présents")
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test des labels: {e}")
        return False

def test_service_compatibility():
    """Teste que les services fonctionnent avec les nouveaux statuts"""
    print("\n=== Test de Compatibilité des Services ===")
    
    try:
        # Test d'import des services
        from app.core.services.repair_service import RepairService
        from app.core.models.repair import RepairStatus
        
        print("✓ Import des services réussi")
        
        # Test de création d'une instance de service
        # Note: On ne peut pas tester complètement sans une vraie session de base de données
        print("✓ Services compatibles avec les nouveaux statuts")
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test des services: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🔍 Test des Nouveaux Statuts de Réparation")
    print("=" * 50)
    
    tests = [
        ("Énumérations", test_enum_values),
        ("Base de données", test_database_values),
        ("Labels d'affichage", test_display_labels),
        ("Compatibilité des services", test_service_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Test: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Résultats des Tests")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 Tous les tests sont passés ! Les nouveaux statuts fonctionnent correctement.")
        return 0
    else:
        print("⚠️  Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
