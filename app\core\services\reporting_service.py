from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import calendar
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, extract
from app.core.models.repair import RepairOrder, RepairStatus, RepairPriority
from app.core.models.maintenance import MaintenanceSchedule
from app.core.models.inventory import InventoryMovement, InventoryItem, ItemStatus
from app.core.models.purchasing import PurchaseOrder, OrderStatus
from app.core.models.equipment import Equipment, EquipmentStatus
from app.core.models.supplier import Supplier
from app.core.models.customer import Customer
from app.core.models.user import User
from app.core.services.base_service import BaseService
from app.utils.database import SessionLocal

class ReportingService:
    def __init__(self, db: Session = None):
        """Initialise le service avec une session de base de données"""
        if db is None:
            db = SessionLocal()
            self._should_close_db = True
        else:
            self._should_close_db = False
        self.db = db

    def __del__(self):
        """Destructeur pour fermer la session si nécessaire"""
        if hasattr(self, '_should_close_db') and self._should_close_db and hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                print("ReportingService: Session de base de données fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

    def refresh_session(self):
        """Rafraîchit la session de base de données"""
        if self._should_close_db and hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                print("ReportingService: Ancienne session fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

        self.db = SessionLocal()
        self._should_close_db = True
        print("ReportingService: Nouvelle session créée")

    async def get_repair_statistics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        repairs = self.db.query(RepairOrder).filter(
            RepairOrder.created_at.between(start_date, end_date)
        ).all()

        total_repairs = len(repairs)
        completed_repairs = len([r for r in repairs if r.status == RepairStatus.COMPLETED])
        invoiced_repairs = len([r for r in repairs if r.status == RepairStatus.INVOICED])
        paid_repairs = len([r for r in repairs if r.status == RepairStatus.PAID])
        waiting_parts_repairs = len([r for r in repairs if r.status == RepairStatus.WAITING_PARTS])
        diagnosed_repairs = len([r for r in repairs if r.status == RepairStatus.DIAGNOSED])
        on_hold_repairs = len([r for r in repairs if r.status == RepairStatus.ON_HOLD])

        average_duration = timedelta()
        total_cost = 0
        total_labor_cost = 0
        total_parts_cost = 0
        total_final_amount = 0
        total_paid_amount = 0
        total_discount_amount = 0
        total_tax_amount = 0

        # Statistiques pour les réparations terminées
        if completed_repairs + invoiced_repairs + paid_repairs > 0:
            finished_repairs = [r for r in repairs if r.status in [RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]]

            durations = [
                (r.completion_date - r.created_at)
                for r in finished_repairs
                if r.completion_date is not None
            ]
            if durations:
                average_duration = sum(durations, timedelta()) / len(durations)

            total_cost = sum(r.total_cost for r in finished_repairs)
            total_labor_cost = sum(r.labor_cost for r in finished_repairs)
            total_parts_cost = sum(r.parts_cost for r in finished_repairs)
            total_final_amount = sum(r.final_amount for r in finished_repairs)
            total_paid_amount = sum(r.total_paid for r in finished_repairs)
            total_discount_amount = sum(r.discount_amount for r in finished_repairs)
            total_tax_amount = sum(r.tax_amount for r in finished_repairs)

        # Calculer le taux de complétion (incluant les réparations facturées et payées)
        finished_count = completed_repairs + invoiced_repairs + paid_repairs
        completion_rate = (finished_count / total_repairs * 100) if total_repairs > 0 else 0

        # Calculer le coût moyen
        average_cost = total_cost / finished_count if finished_count > 0 else 0
        average_final_amount = total_final_amount / finished_count if finished_count > 0 else 0

        # Convertir la durée moyenne en heures
        average_duration_hours = average_duration.total_seconds() / 3600

        # Calculer le taux de paiement
        payment_rate = (paid_repairs / finished_count * 100) if finished_count > 0 else 0

        # Calculer le montant restant à payer
        remaining_amount = total_final_amount - total_paid_amount

        # Statistiques par marque et modèle
        brand_stats = {}
        model_stats = {}

        for repair in repairs:
            # Statistiques par marque
            brand = repair.brand
            if brand not in brand_stats:
                brand_stats[brand] = {
                    "count": 0,
                    "total_cost": 0,
                    "total_final_amount": 0
                }
            brand_stats[brand]["count"] += 1
            if repair.status in [RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]:
                brand_stats[brand]["total_cost"] += repair.total_cost
                brand_stats[brand]["total_final_amount"] += repair.final_amount

            # Statistiques par modèle
            model_key = f"{brand} {repair.model}"
            if model_key not in model_stats:
                model_stats[model_key] = {
                    "count": 0,
                    "total_cost": 0,
                    "total_final_amount": 0
                }
            model_stats[model_key]["count"] += 1
            if repair.status in [RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]:
                model_stats[model_key]["total_cost"] += repair.total_cost
                model_stats[model_key]["total_final_amount"] += repair.final_amount

        # Convertir les dictionnaires en listes pour le tri
        brand_list = [{"brand": brand, **stats} for brand, stats in brand_stats.items()]
        model_list = [{"model": model, **stats} for model, stats in model_stats.items()]

        # Trier par nombre de réparations (décroissant)
        brand_list.sort(key=lambda x: x["count"], reverse=True)
        model_list.sort(key=lambda x: x["count"], reverse=True)

        return {
            "total_repairs": total_repairs,
            "completed_repairs": completed_repairs,
            "invoiced_repairs": invoiced_repairs,
            "paid_repairs": paid_repairs,
            "waiting_parts_repairs": waiting_parts_repairs,
            "diagnosed_repairs": diagnosed_repairs,
            "on_hold_repairs": on_hold_repairs,
            "completion_rate": completion_rate,
            "payment_rate": payment_rate,
            "average_duration_hours": average_duration_hours,
            "total_cost": total_cost,
            "average_cost": average_cost,
            "total_labor_cost": total_labor_cost,
            "total_parts_cost": total_parts_cost,
            "total_final_amount": total_final_amount,
            "average_final_amount": average_final_amount,
            "total_paid_amount": total_paid_amount,
            "remaining_amount": remaining_amount,
            "total_discount_amount": total_discount_amount,
            "total_tax_amount": total_tax_amount,
            "brand_statistics": brand_list[:10],  # Top 10 marques
            "model_statistics": model_list[:10]   # Top 10 modèles
        }

    async def get_inventory_movements(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        movements = self.db.query(
            InventoryMovement.item_id,
            func.sum(InventoryMovement.quantity).label("total_quantity"),
            func.count().label("movement_count")
        ).filter(
            InventoryMovement.created_at.between(start_date, end_date)
        ).group_by(
            InventoryMovement.item_id
        ).all()

        return {
            "total_movements": sum(m.movement_count for m in movements),
            "items_moved": len(movements),
            "movements_by_item": [
                {
                    "item_id": m.item_id,
                    "total_quantity": m.total_quantity,
                    "movement_count": m.movement_count
                }
                for m in movements
            ]
        }

    async def get_maintenance_compliance(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        schedules = self.db.query(MaintenanceSchedule).filter(
            MaintenanceSchedule.next_date.between(start_date, end_date)
        ).all()

        total_scheduled = len(schedules)
        completed_on_time = len([
            s for s in schedules
            if hasattr(s, 'completed_at') and s.completed_at and s.completed_at <= s.next_date
        ])
        completed_late = len([
            s for s in schedules
            if hasattr(s, 'completed_at') and s.completed_at and s.completed_at > s.next_date
        ])
        not_completed = total_scheduled - completed_on_time - completed_late

        return {
            "total_scheduled": total_scheduled,
            "completed_on_time": completed_on_time,
            "completed_late": completed_late,
            "not_completed": not_completed,
            "compliance_rate": (completed_on_time / total_scheduled * 100) if total_scheduled > 0 else 0
        }

    async def generate_monthly_report(self, year: int, month: int) -> Dict[str, Any]:
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1)
        else:
            end_date = datetime(year, month + 1, 1)

        return {
            "period": {
                "year": year,
                "month": month,
                "start_date": start_date,
                "end_date": end_date
            },
            "repair_statistics": await self.get_repair_statistics(start_date, end_date),
            "inventory_movements": await self.get_inventory_movements(start_date, end_date),
            "maintenance_compliance": await self.get_maintenance_compliance(start_date, end_date)
        }

    async def get_technician_performance(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        # Considérer les réparations terminées, facturées et payées
        repairs = self.db.query(
            RepairOrder.technician_name,
            func.count().label('total_repairs'),
            func.sum(RepairOrder.labor_cost).label('total_labor_cost'),
            func.sum(RepairOrder.parts_cost).label('total_parts_cost'),
            func.sum(RepairOrder.total_cost).label('total_cost'),
            func.sum(RepairOrder.final_amount).label('total_final_amount'),
            func.sum(RepairOrder.total_paid).label('total_paid')
        ).filter(
            RepairOrder.created_at.between(start_date, end_date),
            RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]),
            RepairOrder.technician_name.isnot(None)
        ).group_by(RepairOrder.technician_name).all()

        result = []
        for r in repairs:
            # Calculer la durée moyenne des réparations pour ce technicien
            tech_repairs = self.db.query(RepairOrder).filter(
                RepairOrder.created_at.between(start_date, end_date),
                RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]),
                RepairOrder.technician_name == r.technician_name,
                RepairOrder.completion_date.isnot(None)
            ).all()

            average_duration = 0
            if tech_repairs:
                durations = [
                    (repair.completion_date - repair.created_at).total_seconds() / 3600  # Convertir en heures
                    for repair in tech_repairs
                    if repair.completion_date is not None
                ]
                if durations:
                    average_duration = sum(durations) / len(durations)

            # Calculer le taux de réparations payées
            paid_repairs = len([repair for repair in tech_repairs if repair.status == RepairStatus.PAID])
            payment_rate = (paid_repairs / len(tech_repairs) * 100) if tech_repairs else 0

            # Calculer le montant restant à payer
            total_final = float(r.total_final_amount or 0)
            total_paid = float(r.total_paid or 0)
            remaining_amount = total_final - total_paid

            # Calculer le score d'efficacité (réparations par heure)
            total_duration_hours = sum([(repair.completion_date - repair.created_at).total_seconds() / 3600
                                      for repair in tech_repairs
                                      if repair.completion_date is not None]) if tech_repairs else 0
            repairs_per_hour = r.total_repairs / total_duration_hours if total_duration_hours > 0 else 0

            # Calculer le score de rentabilité (profit par heure)
            total_labor = float(r.total_labor_cost or 0)
            total_parts = float(r.total_parts_cost or 0)
            profit = total_labor  # Le profit est principalement basé sur le coût de main-d'œuvre
            profit_per_hour = profit / total_duration_hours if total_duration_hours > 0 else 0

            result.append({
                "technician_id": hash(r.technician_name) % 1000,  # Générer un ID à partir du nom
                "technician_name": r.technician_name,
                "total_repairs": r.total_repairs,
                "average_duration": average_duration,
                "total_duration_hours": total_duration_hours,
                "average_labor_cost": float(r.total_labor_cost or 0) / r.total_repairs if r.total_repairs > 0 else 0,
                "average_parts_cost": float(r.total_parts_cost or 0) / r.total_repairs if r.total_repairs > 0 else 0,
                "total_labor_cost": float(r.total_labor_cost or 0),
                "total_parts_cost": float(r.total_parts_cost or 0),
                "total_cost": float(r.total_cost or 0),
                "total_final_amount": total_final,
                "total_paid": total_paid,
                "remaining_amount": remaining_amount,
                "payment_rate": payment_rate,
                "repairs_per_hour": repairs_per_hour,
                "profit_per_hour": profit_per_hour,
                "efficiency_score": float(r.total_repairs * 100 / (float(r.total_cost or 1)))
            })

        # Trier par nombre total de réparations (décroissant)
        result.sort(key=lambda x: x["total_repairs"], reverse=True)

        return result

    async def get_purchasing_analytics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        orders = self.db.query(
            PurchaseOrder.supplier_id,
            func.count().label('order_count'),
            func.sum(PurchaseOrder.total_amount).label('total_amount'),
            func.avg(PurchaseOrder.lead_time).label('avg_lead_time')
        ).filter(
            PurchaseOrder.created_at.between(start_date, end_date),
            PurchaseOrder.status == OrderStatus.COMPLETED
        ).group_by(PurchaseOrder.supplier_id).all()

        return {
            "total_orders": sum(o.order_count for o in orders),
            "total_spend": sum(float(o.total_amount or 0) for o in orders),
            "average_lead_time": sum(float(o.avg_lead_time or 0) for o in orders) / len(orders) if orders else 0,
            "supplier_performance": [{
                "supplier_id": o.supplier_id,
                "order_count": o.order_count,
                "total_amount": float(o.total_amount or 0),
                "average_lead_time": float(o.avg_lead_time or 0)
            } for o in orders]
        }

    async def get_equipment_reliability(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        # Calculer le nombre de réparations par équipement
        repairs = self.db.query(
            RepairOrder.equipment_id,
            func.count().label('failure_count'),
            func.sum(RepairOrder.total_cost).label('maintenance_cost')
        ).filter(
            RepairOrder.created_at.between(start_date, end_date),
            RepairOrder.equipment_id.isnot(None)
        ).group_by(RepairOrder.equipment_id).all()

        result = []
        for r in repairs:
            # Calculer le temps moyen entre les pannes (MTBF)
            mtbf = 0
            if r.failure_count > 0:
                # Convertir en heures (3600 secondes = 1 heure)
                mtbf = (end_date - start_date).total_seconds() / (r.failure_count * 3600)

            # Récupérer les informations sur l'équipement
            equipment = self.db.query(Equipment).filter(Equipment.id == r.equipment_id).first()
            if equipment:
                result.append({
                    "equipment_id": r.equipment_id,
                    "name": equipment.name,
                    "brand": equipment.brand,
                    "model": equipment.model,
                    "failure_count": r.failure_count,
                    "total_maintenance_cost": float(r.maintenance_cost or 0),
                    "mtbf": float(mtbf),
                    "average_downtime": 24.0  # Valeur par défaut en heures
                })

        return result

    async def get_extended_technician_performance(
        self,
        start_date: datetime,
        end_date: datetime,
        technician_name: str = None
    ) -> List[Dict[str, Any]]:
        """Récupère les performances étendues des techniciens avec détails complets"""

        # Requête de base pour les réparations
        query = self.db.query(RepairOrder).filter(
            RepairOrder.created_at.between(start_date, end_date),
            RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]),
            RepairOrder.technician_name.isnot(None)
        )

        # Filtrer par technicien si spécifié
        if technician_name:
            query = query.filter(RepairOrder.technician_name == technician_name)

        repairs = query.all()

        # Grouper par technicien
        technician_data = {}

        for repair in repairs:
            tech_name = repair.technician_name
            if tech_name not in technician_data:
                technician_data[tech_name] = {
                    'repairs': [],
                    'device_types': {},
                    'brands': set(),
                    'customers': set(),
                    'total_revenue': 0,
                    'total_cost': 0,
                    'total_parts_cost': 0,
                    'total_labor_cost': 0
                }

            tech_data = technician_data[tech_name]
            tech_data['repairs'].append(repair)

            # Analyser les types d'appareils
            device_key = f"{repair.brand} {repair.model}".strip()
            if device_key:
                tech_data['device_types'][device_key] = tech_data['device_types'].get(device_key, 0) + 1
                tech_data['brands'].add(repair.brand)

            # Ajouter le client
            if repair.customer_id:
                tech_data['customers'].add(repair.customer_id)

            # Calculer les totaux
            tech_data['total_revenue'] += getattr(repair, 'final_amount', 0) or 0
            tech_data['total_cost'] += getattr(repair, 'total_cost', 0) or 0
            tech_data['total_parts_cost'] += getattr(repair, 'parts_cost', 0) or 0
            tech_data['total_labor_cost'] += getattr(repair, 'labor_cost', 0) or 0

        # Calculer les métriques pour chaque technicien
        result = []
        for tech_name, data in technician_data.items():
            repairs_list = data['repairs']
            repair_count = len(repairs_list)

            if repair_count == 0:
                continue

            # Calculer la durée moyenne
            durations = []
            for repair in repairs_list:
                if repair.completion_date and repair.created_at:
                    duration = (repair.completion_date - repair.created_at).total_seconds() / 3600
                    durations.append(duration)

            avg_duration = sum(durations) / len(durations) if durations else 0

            # Calculer les métriques de performance
            avg_revenue_per_repair = data['total_revenue'] / repair_count
            profit_margin = ((data['total_revenue'] - data['total_cost']) / data['total_revenue'] * 100) if data['total_revenue'] > 0 else 0

            # Calculer la satisfaction client (basée sur les réparations terminées sans retour)
            completed_repairs = [r for r in repairs_list if r.status in [RepairStatus.COMPLETED, RepairStatus.PAID]]
            satisfaction_score = (len(completed_repairs) / repair_count * 100) if repair_count > 0 else 0

            # Calculer l'efficacité (réparations par heure)
            total_work_hours = sum(durations) if durations else repair_count * 4  # 4h par défaut
            efficiency = repair_count / total_work_hours if total_work_hours > 0 else 0

            # Top 5 des types d'appareils
            top_devices = sorted(data['device_types'].items(), key=lambda x: x[1], reverse=True)[:5]

            result.append({
                'technician_name': tech_name,
                'total_repairs': repair_count,
                'completed_repairs': len(completed_repairs),
                'in_progress_repairs': repair_count - len(completed_repairs),
                'average_duration': round(avg_duration, 2),
                'total_revenue': round(data['total_revenue'], 2),
                'total_cost': round(data['total_cost'], 2),
                'total_parts_cost': round(data['total_parts_cost'], 2),
                'total_labor_cost': round(data['total_labor_cost'], 2),
                'avg_revenue_per_repair': round(avg_revenue_per_repair, 2),
                'profit_margin': round(profit_margin, 1),
                'customer_satisfaction': round(satisfaction_score, 1),
                'efficiency_repairs_per_hour': round(efficiency, 2),
                'unique_customers': len(data['customers']),
                'device_types_count': len(data['device_types']),
                'brands_count': len(data['brands']),
                'top_devices': top_devices,
                'device_types': list(data['device_types'].keys()),
                'brands_repaired': list(data['brands']),
                'parts_used_count': sum(1 for r in repairs_list if getattr(r, 'parts_cost', 0) > 0),
                'repeat_customers': len(data['customers']) - repair_count if len(data['customers']) < repair_count else 0
            })

        # Trier par nombre de réparations
        result.sort(key=lambda x: x['total_repairs'], reverse=True)

        return result

    async def get_technician_timeline_data(
        self,
        start_date: datetime,
        end_date: datetime,
        technician_name: str = None
    ) -> Dict[str, Any]:
        """Récupère les données d'évolution temporelle des techniciens"""

        # Diviser la période en intervalles mensuels
        current_date = start_date.replace(day=1)  # Premier jour du mois
        monthly_data = {}

        while current_date <= end_date:
            # Calculer la fin du mois
            if current_date.month == 12:
                next_month = current_date.replace(year=current_date.year + 1, month=1)
            else:
                next_month = current_date.replace(month=current_date.month + 1)

            month_end = next_month - timedelta(days=1)
            if month_end > end_date:
                month_end = end_date

            # Récupérer les données pour ce mois
            month_key = current_date.strftime("%Y-%m")
            monthly_performance = await self.get_extended_technician_performance(
                current_date, month_end, technician_name
            )

            monthly_data[month_key] = {
                'period': current_date.strftime("%b %Y"),
                'technicians': monthly_performance,
                'total_repairs': sum(t['total_repairs'] for t in monthly_performance),
                'total_revenue': sum(t['total_revenue'] for t in monthly_performance),
                'avg_satisfaction': sum(t['customer_satisfaction'] for t in monthly_performance) / len(monthly_performance) if monthly_performance else 0
            }

            current_date = next_month

        return {
            'monthly_data': monthly_data,
            'period': {'start': start_date, 'end': end_date},
            'technician_filter': technician_name
        }

    async def get_technician_comparison_metrics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Récupère les métriques de comparaison entre techniciens"""

        technicians_data = await self.get_extended_technician_performance(start_date, end_date)

        if not technicians_data:
            return {}

        # Calculer les statistiques de comparaison
        repair_counts = [t['total_repairs'] for t in technicians_data]
        revenues = [t['total_revenue'] for t in technicians_data]
        satisfactions = [t['customer_satisfaction'] for t in technicians_data]
        efficiencies = [t['efficiency_repairs_per_hour'] for t in technicians_data]

        return {
            'technicians_count': len(technicians_data),
            'total_repairs': sum(repair_counts),
            'total_revenue': sum(revenues),
            'avg_repairs_per_tech': sum(repair_counts) / len(repair_counts),
            'avg_revenue_per_tech': sum(revenues) / len(revenues),
            'avg_satisfaction': sum(satisfactions) / len(satisfactions),
            'avg_efficiency': sum(efficiencies) / len(efficiencies),
            'top_performer_repairs': max(technicians_data, key=lambda x: x['total_repairs']),
            'top_performer_revenue': max(technicians_data, key=lambda x: x['total_revenue']),
            'top_performer_satisfaction': max(technicians_data, key=lambda x: x['customer_satisfaction']),
            'performance_distribution': {
                'repairs': {
                    'min': min(repair_counts),
                    'max': max(repair_counts),
                    'avg': sum(repair_counts) / len(repair_counts)
                },
                'revenue': {
                    'min': min(revenues),
                    'max': max(revenues),
                    'avg': sum(revenues) / len(revenues)
                },
                'satisfaction': {
                    'min': min(satisfactions),
                    'max': max(satisfactions),
                    'avg': sum(satisfactions) / len(satisfactions)
                }
            }
        }

    async def generate_quarterly_report(
        self,
        year: int,
        quarter: int
    ) -> Dict[str, Any]:
        start_month = (quarter - 1) * 3 + 1
        start_date = datetime(year, start_month, 1)
        if start_month + 3 > 12:
            end_date = datetime(year + 1, ((start_month + 3) % 12), 1)
        else:
            end_date = datetime(year, start_month + 3, 1)

        return {
            "period": {
                "year": year,
                "quarter": quarter,
                "start_date": start_date,
                "end_date": end_date
            },
            "repair_statistics": await self.get_repair_statistics(start_date, end_date),
            "inventory_movements": await self.get_inventory_movements(start_date, end_date),
            "maintenance_compliance": await self.get_maintenance_compliance(start_date, end_date),
            "technician_performance": await self.get_technician_performance(start_date, end_date),
            "purchasing_analytics": await self.get_purchasing_analytics(start_date, end_date),
            "equipment_reliability": await self.get_equipment_reliability(start_date, end_date)
        }

    async def generate_annual_report(self, year: int) -> Dict[str, Any]:
        """Génère un rapport annuel pour l'année spécifiée"""
        start_date = datetime(year, 1, 1)
        end_date = datetime(year + 1, 1, 1)

        # Récupérer les rapports trimestriels
        q1_report = await self.generate_quarterly_report(year, 1)
        q2_report = await self.generate_quarterly_report(year, 2)
        q3_report = await self.generate_quarterly_report(year, 3)
        q4_report = await self.generate_quarterly_report(year, 4)

        # Récupérer les statistiques annuelles
        repair_stats = await self.get_repair_statistics(start_date, end_date)
        inventory_stats = await self.get_inventory_movements(start_date, end_date)
        maintenance_stats = await self.get_maintenance_compliance(start_date, end_date)
        technician_stats = await self.get_technician_performance(start_date, end_date)
        purchasing_stats = await self.get_purchasing_analytics(start_date, end_date)
        equipment_stats = await self.get_equipment_reliability(start_date, end_date)

        return {
            "period": {
                "year": year,
                "start_date": start_date,
                "end_date": end_date
            },
            "annual_summary": {
                "repair_statistics": repair_stats,
                "inventory_movements": inventory_stats,
                "maintenance_compliance": maintenance_stats,
                "technician_performance": technician_stats,
                "purchasing_analytics": purchasing_stats,
                "equipment_reliability": equipment_stats
            },
            "quarterly_reports": {
                "q1": q1_report,
                "q2": q2_report,
                "q3": q3_report,
                "q4": q4_report
            }
        }

    async def get_dashboard_kpis(self) -> Dict[str, Any]:
        """Récupère les KPIs pour le tableau de bord"""
        # Date actuelle et date il y a 30 jours
        now = datetime.now()
        thirty_days_ago = now - timedelta(days=30)

        # Réparations en cours (incluant les en attente et en pause)
        active_repairs = self.db.query(RepairOrder).filter(
            RepairOrder.status.in_([
                RepairStatus.PENDING,
                RepairStatus.IN_PROGRESS,
                RepairStatus.ON_HOLD
            ])
        ).count()

        # Articles en stock faible
        low_stock_items = self.db.query(InventoryItem).filter(
            InventoryItem.quantity <= InventoryItem.minimum_quantity,
            InventoryItem.is_active == True
        ).count()

        # Quantité totale d'articles en stock
        total_stock_quantity = self.db.query(func.sum(InventoryItem.quantity)).filter(
            InventoryItem.is_active == True
        ).scalar() or 0

        # Maintenances prévues dans les 7 prochains jours
        next_week = now + timedelta(days=7)
        upcoming_maintenance = self.db.query(MaintenanceSchedule).filter(
            MaintenanceSchedule.next_date.between(now, next_week)
        ).count()

        # Commandes en attente
        pending_orders = self.db.query(PurchaseOrder).filter(
            PurchaseOrder.status.in_([OrderStatus.PENDING, OrderStatus.APPROVED])
        ).count()

        # Équipements hors service
        out_of_service = self.db.query(Equipment).filter(
            Equipment.status == EquipmentStatus.OUT_OF_SERVICE,
            Equipment.is_active == True
        ).count()

        # Réparations terminées ce mois-ci (incluant les facturées et payées)
        first_day_of_month = datetime(now.year, now.month, 1)
        completed_repairs = self.db.query(RepairOrder).filter(
            RepairOrder.status.in_([
                RepairStatus.COMPLETED,
                RepairStatus.INVOICED,
                RepairStatus.PAID
            ]),
            RepairOrder.completion_date >= first_day_of_month
        ).count()

        # Réparations en attente de paiement
        unpaid_repairs = self.db.query(RepairOrder).filter(
            RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED]),
        ).count()

        # Montant total des réparations impayées (terminées mais non entièrement payées)
        unpaid_amount = self.db.query(func.sum(RepairOrder.final_amount - RepairOrder.total_paid)).filter(
            RepairOrder.status == RepairStatus.COMPLETED,
            RepairOrder.final_amount > RepairOrder.total_paid
        ).scalar() or 0

        # Réparations en pause
        on_hold = self.db.query(RepairOrder).filter(
            RepairOrder.status == RepairStatus.ON_HOLD
        ).count()

        # Chiffre d'affaires des réparations ce mois-ci
        monthly_revenue = self.db.query(func.sum(RepairOrder.final_amount)).filter(
            RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]),
            RepairOrder.completion_date >= first_day_of_month
        ).scalar() or 0

        # Paiements reçus ce mois-ci
        monthly_payments = self.db.query(func.sum(RepairOrder.total_paid)).filter(
            RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]),
            RepairOrder.payment_date >= first_day_of_month
        ).scalar() or 0

        return {
            "active_repairs": active_repairs,
            "low_stock_items": low_stock_items,
            "total_stock_quantity": int(total_stock_quantity),
            "upcoming_maintenance": upcoming_maintenance,
            "pending_orders": pending_orders,
            "out_of_service_equipment": out_of_service,
            "completed_repairs_this_month": completed_repairs,
            "unpaid_repairs": unpaid_repairs,
            "unpaid_amount": float(unpaid_amount),
            "waiting_parts": waiting_parts,
            "on_hold": on_hold,
            "monthly_revenue": float(monthly_revenue),
            "monthly_payments": float(monthly_payments)
        }

    async def get_repair_trend_data(self, months: int = 12) -> List[Dict[str, Any]]:
        """Récupère les données de tendance des réparations sur plusieurs mois"""
        now = datetime.now()
        result = []

        for i in range(months - 1, -1, -1):
            # Calculer le premier jour du mois
            year = now.year - ((now.month - i - 1) // 12)
            month = ((now.month - i - 1) % 12) + 1
            start_date = datetime(year, month, 1)

            # Calculer le premier jour du mois suivant
            if month == 12:
                end_date = datetime(year + 1, 1, 1)
            else:
                end_date = datetime(year, month + 1, 1)

            # Compter les réparations par statut
            repairs = self.db.query(
                RepairOrder.status,
                func.count().label('count')
            ).filter(
                RepairOrder.created_at.between(start_date, end_date)
            ).group_by(RepairOrder.status).all()

            # Récupérer les données financières
            financial_data = self.db.query(
                func.sum(RepairOrder.final_amount).label('revenue'),
                func.sum(RepairOrder.total_paid).label('payments'),
                func.sum(RepairOrder.labor_cost).label('labor_cost'),
                func.sum(RepairOrder.parts_cost).label('parts_cost')
            ).filter(
                RepairOrder.created_at.between(start_date, end_date),
                RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID])
            ).first()

            # Créer un dictionnaire pour ce mois
            month_data = {
                "year": year,
                "month": month,
                "month_name": calendar.month_name[month],
                "total": 0,
                "revenue": float(financial_data.revenue or 0) if financial_data else 0,
                "payments": float(financial_data.payments or 0) if financial_data else 0,
                "labor_cost": float(financial_data.labor_cost or 0) if financial_data else 0,
                "parts_cost": float(financial_data.parts_cost or 0) if financial_data else 0
            }

            # Ajouter les comptages par statut
            for status, count in repairs:
                month_data[status.value] = count
                month_data["total"] += count

            # Regrouper les statuts pour faciliter l'affichage
            completed = month_data.get("completed", 0)
            invoiced = month_data.get("invoiced", 0)
            paid = month_data.get("paid", 0)
            in_progress = month_data.get("in_progress", 0)
            diagnosed = month_data.get("diagnosed", 0)
            waiting_parts = month_data.get("waiting_parts", 0)
            pending = month_data.get("pending", 0)
            cancelled = month_data.get("cancelled", 0)
            on_hold = month_data.get("on_hold", 0)

            # Ajouter les groupes
            month_data["finished"] = completed + invoiced + paid
            month_data["in_progress_group"] = in_progress + diagnosed + waiting_parts
            month_data["other_group"] = pending + cancelled + on_hold

            # Calculer le profit (revenu - coûts des pièces)
            month_data["profit"] = month_data["revenue"] - month_data["parts_cost"]

            # Calculer le montant restant à payer
            month_data["remaining"] = month_data["revenue"] - month_data["payments"]

            result.append(month_data)

        return result

    async def get_inventory_trend_data(self, months: int = 12) -> List[Dict[str, Any]]:
        """Récupère les données de tendance des mouvements d'inventaire sur plusieurs mois"""
        now = datetime.now()
        result = []

        for i in range(months - 1, -1, -1):
            # Calculer le premier jour du mois
            year = now.year - ((now.month - i - 1) // 12)
            month = ((now.month - i - 1) % 12) + 1
            start_date = datetime(year, month, 1)

            # Calculer le premier jour du mois suivant
            if month == 12:
                end_date = datetime(year + 1, 1, 1)
            else:
                end_date = datetime(year, month + 1, 1)

            # Compter les mouvements par type
            movements = self.db.query(
                InventoryMovement.type,
                func.count().label('count'),
                func.sum(InventoryMovement.quantity).label('total_quantity')
            ).filter(
                InventoryMovement.created_at.between(start_date, end_date)
            ).group_by(InventoryMovement.type).all()

            # Créer un dictionnaire pour ce mois
            month_data = {
                "year": year,
                "month": month,
                "month_name": calendar.month_name[month],
                "total_movements": 0,
                "in_quantity": 0,
                "out_quantity": 0
            }

            # Ajouter les comptages par type
            for movement_type, count, quantity in movements:
                if movement_type == "in":
                    month_data["in_quantity"] = float(quantity or 0)
                elif movement_type == "out":
                    month_data["out_quantity"] = float(quantity or 0)
                month_data["total_movements"] += count

            result.append(month_data)

        return result

    async def get_top_repaired_equipment(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Récupère les équipements les plus réparés"""
        # Période: dernière année
        one_year_ago = datetime.now() - timedelta(days=365)

        equipment_repairs = self.db.query(
            RepairOrder.equipment_id,
            func.count().label('repair_count'),
            func.sum(RepairOrder.total_cost).label('total_cost')
        ).filter(
            RepairOrder.created_at >= one_year_ago,
            RepairOrder.equipment_id.isnot(None)
        ).group_by(
            RepairOrder.equipment_id
        ).order_by(
            desc('repair_count')
        ).limit(limit).all()

        result = []
        for equipment_id, repair_count, total_cost in equipment_repairs:
            equipment = self.db.query(Equipment).filter(Equipment.id == equipment_id).first()
            if equipment:
                result.append({
                    "equipment_id": equipment_id,
                    "name": equipment.name,
                    "brand": equipment.brand,
                    "model": equipment.model,
                    "repair_count": repair_count,
                    "total_cost": float(total_cost or 0)
                })

        return result

    async def get_top_customers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Récupère les clients avec le plus de réparations"""
        # Période: dernière année
        one_year_ago = datetime.now() - timedelta(days=365)

        customer_repairs = self.db.query(
            RepairOrder.customer_name,
            func.count().label('repair_count'),
            func.sum(RepairOrder.total_cost).label('total_cost')
        ).filter(
            RepairOrder.created_at >= one_year_ago
        ).group_by(
            RepairOrder.customer_name
        ).order_by(
            desc('repair_count')
        ).limit(limit).all()

        return [{
            "customer_name": customer_name,
            "repair_count": repair_count,
            "total_cost": float(total_cost or 0)
        } for customer_name, repair_count, total_cost in customer_repairs]

    async def get_repair_status_distribution(self) -> Dict[str, int]:
        """Récupère la distribution des statuts de réparation"""
        status_counts = self.db.query(
            RepairOrder.status,
            func.count().label('count')
        ).group_by(RepairOrder.status).all()

        result = {status.value: 0 for status in RepairStatus}
        for status, count in status_counts:
            result[status.value] = count

        # Ajouter des statistiques supplémentaires
        total_repairs = sum(result.values())

        # Calculer les pourcentages
        percentages = {}
        for status, count in result.items():
            percentages[f"{status}_percent"] = (count / total_repairs * 100) if total_repairs > 0 else 0

        # Regrouper les statuts
        in_progress_count = result.get("in_progress", 0) + result.get("diagnosed", 0) + result.get("waiting_parts", 0)
        finished_count = result.get("completed", 0) + result.get("invoiced", 0) + result.get("paid", 0)
        other_count = result.get("cancelled", 0) + result.get("on_hold", 0) + result.get("pending", 0)

        # Ajouter les groupes au résultat
        result.update({
            "total": total_repairs,
            "in_progress_group": in_progress_count,
            "finished_group": finished_count,
            "other_group": other_count,
            "in_progress_percent": (in_progress_count / total_repairs * 100) if total_repairs > 0 else 0,
            "finished_percent": (finished_count / total_repairs * 100) if total_repairs > 0 else 0,
            "other_percent": (other_count / total_repairs * 100) if total_repairs > 0 else 0
        })

        # Ajouter les pourcentages
        result.update(percentages)

        return result

    async def get_executive_kpis(self) -> Dict[str, Any]:
        """
        Récupère les KPIs pour le tableau de bord exécutif

        Returns:
            Dictionnaire contenant les KPIs exécutifs
        """
        try:
            # Récupérer les KPIs de base
            base_kpis = await self.get_dashboard_kpis()

            # Calculer des métriques supplémentaires
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            last_month = today - timedelta(days=30)

            # Chiffre d'affaires et profit (simulation basée sur les réparations)
            revenue_query = self.db.query(func.sum(RepairOrder.final_amount)).filter(
                RepairOrder.completion_date >= last_month,
                RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID])
            )
            total_revenue = revenue_query.scalar() or 0

            # Coût total (estimation basée sur les pièces et main d'œuvre)
            cost_query = self.db.query(
                func.sum(RepairOrder.parts_cost + RepairOrder.labor_cost)
            ).filter(
                RepairOrder.completion_date >= last_month,
                RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID])
            )
            total_cost = cost_query.scalar() or 0

            # Profit net
            profit = total_revenue - total_cost
            margin = (profit / total_revenue * 100) if total_revenue > 0 else 0

            # Flux de trésorerie (simulation)
            cash_flow = profit * 0.8  # Estimation

            # Temps moyen de réparation
            avg_duration_query = self.db.query(
                func.avg(
                    func.extract('epoch', RepairOrder.completion_date - RepairOrder.created_at) / 86400
                )
            ).filter(
                RepairOrder.completion_date.isnot(None),
                RepairOrder.completion_date >= last_month
            )
            avg_repair_time = avg_duration_query.scalar() or 0

            # Satisfaction client (simulation basée sur les réparations terminées)
            completed_repairs = self.db.query(func.count(RepairOrder.id)).filter(
                RepairOrder.status == RepairStatus.COMPLETED,
                RepairOrder.completion_date >= last_month
            ).scalar() or 0

            customer_satisfaction = min(95, 70 + (completed_repairs / 10))

            # Efficacité opérationnelle (simulation)
            efficiency = min(100, 60 + (completed_repairs / 5))

            # Nouveaux clients (simulation)
            total_customers = self.db.query(func.count(func.distinct(RepairOrder.customer_name))).scalar() or 0
            new_customers = max(0, total_customers // 10)

            # Clients récurrents
            repeat_rate = min(80, 30 + (completed_repairs / 20))

            # Valeur moyenne de commande
            avg_order_value = total_revenue / max(1, completed_repairs)

            # Taux de croissance (simulation)
            growth_rate = min(25, max(-10, (profit / 10000) * 2))

            return {
                'revenue': total_revenue,
                'profit': profit,
                'margin': margin,
                'cash_flow': cash_flow,
                'repairs': completed_repairs,
                'avg_repair_time': avg_repair_time,
                'customer_satisfaction': customer_satisfaction,
                'efficiency': efficiency,
                'new_customers': new_customers,
                'repeat_customers': repeat_rate,
                'avg_order_value': avg_order_value,
                'growth_rate': growth_rate
            }

        except Exception as e:
            print(f"Erreur lors de la récupération des KPIs exécutifs: {e}")
            return {
                'revenue': 0, 'profit': 0, 'margin': 0, 'cash_flow': 0,
                'repairs': 0, 'avg_repair_time': 0, 'customer_satisfaction': 0,
                'efficiency': 0, 'new_customers': 0, 'repeat_customers': 0,
                'avg_order_value': 0, 'growth_rate': 0
            }

    async def get_period_summary(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Récupère un résumé des données pour une période donnée

        Args:
            start_date: Date de début
            end_date: Date de fin

        Returns:
            Dictionnaire contenant le résumé de la période
        """
        try:
            # Nombre total de réparations
            total_repairs = self.db.query(func.count(RepairOrder.id)).filter(
                RepairOrder.created_at >= start_date,
                RepairOrder.created_at <= end_date
            ).scalar() or 0

            # Chiffre d'affaires total
            total_revenue = self.db.query(func.sum(RepairOrder.final_amount)).filter(
                RepairOrder.completion_date >= start_date,
                RepairOrder.completion_date <= end_date,
                RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID])
            ).scalar() or 0

            # Coût total
            total_cost = self.db.query(
                func.sum(RepairOrder.parts_cost + RepairOrder.labor_cost)
            ).filter(
                RepairOrder.completion_date >= start_date,
                RepairOrder.completion_date <= end_date,
                RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID])
            ).scalar() or 0

            # Profit
            total_profit = total_revenue - total_cost

            # Durée moyenne
            avg_duration = self.db.query(
                func.avg(
                    func.extract('epoch', RepairOrder.completion_date - RepairOrder.created_at) / 3600
                )
            ).filter(
                RepairOrder.completion_date.isnot(None),
                RepairOrder.completion_date >= start_date,
                RepairOrder.completion_date <= end_date
            ).scalar() or 0

            # Taux de complétion
            completed_repairs = self.db.query(func.count(RepairOrder.id)).filter(
                RepairOrder.status.in_([RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]),
                RepairOrder.completion_date >= start_date,
                RepairOrder.completion_date <= end_date
            ).scalar() or 0

            completion_rate = (completed_repairs / max(1, total_repairs)) * 100

            return {
                'total_repairs': total_repairs,
                'total_revenue': total_revenue,
                'total_cost': total_cost,
                'total_profit': total_profit,
                'avg_duration': avg_duration,
                'completion_rate': completion_rate,
                'completed_repairs': completed_repairs
            }

        except Exception as e:
            print(f"Erreur lors de la récupération du résumé de période: {e}")
            return {
                'total_repairs': 0, 'total_revenue': 0, 'total_cost': 0,
                'total_profit': 0, 'avg_duration': 0, 'completion_rate': 0,
                'completed_repairs': 0
            }



