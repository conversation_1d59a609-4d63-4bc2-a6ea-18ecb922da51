#!/usr/bin/env python3
"""
Test du service de reporting après la correction des statuts
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Ajouter le répertoire racine au path
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_reporting_service():
    """Teste le service de reporting avec les nouveaux statuts"""
    print("=== Test du Service de Reporting ===")
    
    try:
        # Import des modules nécessaires
        from app.utils.database import SessionLocal
        from app.core.services.reporting_service import ReportingService
        from app.core.models.repair import RepairStatus
        
        print("✓ Import des modules réussi")
        
        # Créer une session et le service
        db = SessionLocal()
        service = ReportingService(db)
        
        print("✓ Service de reporting créé")
        
        # Tester les méthodes principales
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        # Test des statistiques de réparation
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            stats = loop.run_until_complete(service.get_repair_statistics(start_date, end_date))
            print("✓ Statistiques de réparation récupérées")
            print(f"  - Total réparations: {stats.get('total_repairs', 0)}")
            print(f"  - Réparations terminées: {stats.get('completed_repairs', 0)}")
            print(f"  - Réparations en cours: {stats.get('in_progress_repairs', 0)}")
            print(f"  - Réparations en pause: {stats.get('on_hold_repairs', 0)}")
            
        except Exception as e:
            print(f"✗ Erreur lors du test des statistiques de réparation: {e}")
            return False
        
        # Test des KPIs du tableau de bord
        try:
            kpis = loop.run_until_complete(service.get_dashboard_kpis())
            print("✓ KPIs du tableau de bord récupérés")
            print(f"  - Réparations actives: {kpis.get('active_repairs', 0)}")
            print(f"  - Réparations terminées ce mois: {kpis.get('completed_repairs', 0)}")
            
        except Exception as e:
            print(f"✗ Erreur lors du test des KPIs: {e}")
            return False
        
        # Test des performances des techniciens
        try:
            tech_perf = loop.run_until_complete(service.get_technician_performance(start_date, end_date))
            print("✓ Performances des techniciens récupérées")
            print(f"  - Nombre de techniciens: {len(tech_perf)}")
            
        except Exception as e:
            print(f"✗ Erreur lors du test des performances des techniciens: {e}")
            return False
        
        loop.close()
        db.close()
        
        print("✓ Tous les tests du service de reporting sont passés")
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test du service de reporting: {e}")
        return False

def test_enum_consistency():
    """Teste la cohérence des énumérations"""
    print("\n=== Test de Cohérence des Énumérations ===")
    
    try:
        from app.core.models.repair import RepairStatus
        
        # Vérifier que tous les nouveaux statuts sont présents
        expected_statuses = ['pending', 'in_progress', 'on_hold', 'completed', 'cancelled']
        actual_statuses = [status.value for status in RepairStatus]
        
        print(f"Statuts attendus: {expected_statuses}")
        print(f"Statuts actuels: {actual_statuses}")
        
        if set(expected_statuses) == set(actual_statuses):
            print("✓ Énumération RepairStatus correcte")
            return True
        else:
            missing = set(expected_statuses) - set(actual_statuses)
            extra = set(actual_statuses) - set(expected_statuses)
            if missing:
                print(f"✗ Statuts manquants: {missing}")
            if extra:
                print(f"✗ Statuts en trop: {extra}")
            return False
        
    except Exception as e:
        print(f"✗ Erreur lors du test des énumérations: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🔍 Test du Service de Reporting après Correction des Statuts")
    print("=" * 60)
    
    tests = [
        ("Cohérence des énumérations", test_enum_consistency),
        ("Service de reporting", test_reporting_service)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📊 Résultats des Tests")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 SUCCÈS ! Tous les tests sont passés.")
        print("Le service de reporting fonctionne correctement avec les nouveaux statuts.")
    else:
        print("❌ ÉCHEC ! Certains tests ont échoué.")
        print("Vérifiez les erreurs ci-dessus.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
