#!/usr/bin/env python3
"""
Script pour corriger la cohérence entre les énumérations de statuts de réparation
et s'assurer que les données en base correspondent aux énumérations.
"""

import sqlite3
import sys
import os
from datetime import datetime

def fix_enum_consistency():
    """Corrige la cohérence entre les énumérations et les données"""
    
    db_path = "data/app.db"
    if not os.path.exists(db_path):
        print(f"Base de données non trouvée: {db_path}")
        return False
    
    # Créer une sauvegarde
    backup_path = f"{db_path}.backup_enum_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✓ Sauvegarde créée : {backup_path}")
    except Exception as e:
        print(f"✗ Erreur lors de la sauvegarde : {e}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Correction de la cohérence des énumérations...")
        
        # Vérifier les valeurs actuelles
        cursor.execute("SELECT DISTINCT status FROM repair_orders WHERE status IS NOT NULL")
        current_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Statuts actuels dans repair_orders: {current_statuses}")
        
        # Mapping pour normaliser vers les valeurs attendues par l'énumération database/models.py
        status_mapping = {
            # Minuscules vers majuscules
            'pending': 'PENDING',
            'in_progress': 'IN_PROGRESS', 
            'on_hold': 'ON_HOLD',
            'completed': 'COMPLETED',
            'cancelled': 'CANCELLED',
            
            # Anciens statuts vers nouveaux (majuscules)
            'diagnosed': 'COMPLETED',
            'DIAGNOSED': 'COMPLETED',
            'waiting_parts': 'ON_HOLD',
            'WAITING_PARTS': 'ON_HOLD',
            'waiting_for_parts': 'ON_HOLD',
            'WAITING_FOR_PARTS': 'ON_HOLD',
            'waiting_for_customer': 'ON_HOLD',
            'WAITING_FOR_CUSTOMER': 'ON_HOLD',
            'ready_for_pickup': 'COMPLETED',
            'READY_FOR_PICKUP': 'COMPLETED',
            'delivered': 'COMPLETED',
            'DELIVERED': 'COMPLETED',
            'invoiced': 'COMPLETED',
            'INVOICED': 'COMPLETED',
            'paid': 'COMPLETED',
            'PAID': 'COMPLETED',
        }
        
        # Appliquer les mappings pour repair_orders
        updates_count = 0
        for old_status, new_status in status_mapping.items():
            cursor.execute(
                "UPDATE repair_orders SET status = ? WHERE status = ?",
                (new_status, old_status)
            )
            count = cursor.rowcount
            if count > 0:
                print(f"✓ {count} réparations mises à jour de '{old_status}' vers '{new_status}'")
                updates_count += count
        
        # Appliquer les mappings pour repair_status_history
        history_updates = 0
        for old_status, new_status in status_mapping.items():
            cursor.execute(
                "UPDATE repair_status_history SET status = ? WHERE status = ?",
                (new_status, old_status)
            )
            count = cursor.rowcount
            if count > 0:
                print(f"✓ {count} entrées d'historique mises à jour de '{old_status}' vers '{new_status}'")
                history_updates += count
        
        # Vérifier les résultats finaux
        cursor.execute("SELECT DISTINCT status FROM repair_orders WHERE status IS NOT NULL")
        final_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Statuts finaux dans repair_orders: {final_statuses}")
        
        cursor.execute("SELECT DISTINCT status FROM repair_status_history WHERE status IS NOT NULL")
        final_history_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Statuts finaux dans l'historique: {final_history_statuses}")
        
        # Vérifier que tous les statuts sont valides
        valid_statuses = ['PENDING', 'IN_PROGRESS', 'ON_HOLD', 'COMPLETED', 'CANCELLED']
        invalid_statuses = [s for s in final_statuses if s not in valid_statuses]
        invalid_history_statuses = [s for s in final_history_statuses if s not in valid_statuses]
        
        if invalid_statuses:
            print(f"✗ Statuts invalides restants dans repair_orders: {invalid_statuses}")
            return False
        
        if invalid_history_statuses:
            print(f"✗ Statuts invalides restants dans l'historique: {invalid_history_statuses}")
            return False
        
        # Compter les réparations par statut
        cursor.execute("SELECT status, COUNT(*) FROM repair_orders GROUP BY status")
        status_counts = cursor.fetchall()
        print("Répartition finale des statuts:")
        for status, count in status_counts:
            print(f"  - {status}: {count} réparations")
        
        # Valider les modifications
        conn.commit()
        print(f"✓ Correction terminée avec succès")
        print(f"✓ {updates_count} réparations mises à jour")
        print(f"✓ {history_updates} entrées d'historique mises à jour")
        return True
        
    except Exception as e:
        # Annuler les modifications en cas d'erreur
        conn.rollback()
        print(f"✗ Erreur lors de la correction: {str(e)}")
        print(f"La sauvegarde est disponible à: {backup_path}")
        return False
        
    finally:
        # Fermer la connexion
        conn.close()

def main():
    """Fonction principale"""
    print("🔧 Correction de la Cohérence des Énumérations")
    print("=" * 50)
    
    success = fix_enum_consistency()
    
    if success:
        print("\n🎉 Correction terminée avec succès !")
        print("Les énumérations et les données sont maintenant cohérentes.")
        print("\nStatuts de réparation valides:")
        print("- PENDING (En attente)")
        print("- IN_PROGRESS (En cours)")
        print("- ON_HOLD (En pause)")
        print("- COMPLETED (Terminé)")
        print("- CANCELLED (Annulé)")
    else:
        print("\n❌ La correction a échoué.")
        print("Vérifiez les erreurs ci-dessus et la sauvegarde.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
