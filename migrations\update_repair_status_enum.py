#!/usr/bin/env python3
"""
Script de migration pour mettre à jour les statuts de réparation
selon les nouvelles spécifications :
- Statuts de réparation : "pending", "in_progress", "on_hold", "completed", "cancelled"
- Ajout du statut "delivered" aux statuts de paiement
"""

import sqlite3
import sys
import os
from datetime import datetime

def backup_database(db_path):
    """Crée une sauvegarde de la base de données"""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✓ Sauvegarde créée : {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ Erreur lors de la sauvegarde : {e}")
        return None

def update_repair_statuses(db_path):
    """Met à jour les statuts de réparation dans la base de données"""
    
    if not os.path.exists(db_path):
        print(f"Base de données non trouvée à: {db_path}")
        return False
    
    # Créer une sauvegarde
    backup_path = backup_database(db_path)
    if not backup_path:
        print("Impossible de créer une sauvegarde. Migration annulée.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Mise à jour des statuts de réparation...")
        
        # Vérifier les valeurs actuelles dans repair_orders
        cursor.execute("SELECT DISTINCT status FROM repair_orders WHERE status IS NOT NULL")
        current_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Statuts actuels dans repair_orders: {current_statuses}")
        
        # Mapping des anciens statuts vers les nouveaux
        status_mapping = {
            'diagnosed': 'completed',  # Diagnostiqué -> Terminé
            'waiting_parts': 'on_hold',  # En attente de pièces -> En pause
            'waiting_for_parts': 'on_hold',  # En attente de pièces -> En pause
            'waiting_for_customer': 'on_hold',  # En attente du client -> En pause
            'ready_for_pickup': 'completed',  # Prêt pour récupération -> Terminé
            'delivered': 'completed',  # Livré -> Terminé
            'invoiced': 'completed',  # Facturé -> Terminé
            'paid': 'completed',  # Payé -> Terminé
        }
        
        # Appliquer les mappings
        updates_count = 0
        for old_status, new_status in status_mapping.items():
            cursor.execute(
                "UPDATE repair_orders SET status = ? WHERE status = ?",
                (new_status, old_status)
            )
            count = cursor.rowcount
            if count > 0:
                print(f"✓ {count} réparations mises à jour de '{old_status}' vers '{new_status}'")
                updates_count += count
        
        # Vérifier les valeurs dans repair_status_history
        cursor.execute("SELECT DISTINCT status FROM repair_status_history WHERE status IS NOT NULL")
        history_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Statuts dans l'historique: {history_statuses}")
        
        # Mettre à jour l'historique des statuts
        history_updates = 0
        for old_status, new_status in status_mapping.items():
            cursor.execute(
                "UPDATE repair_status_history SET status = ? WHERE status = ?",
                (new_status, old_status)
            )
            count = cursor.rowcount
            if count > 0:
                print(f"✓ {count} entrées d'historique mises à jour de '{old_status}' vers '{new_status}'")
                history_updates += count
        
        # Vérifier les statuts de paiement et ajouter "delivered" si nécessaire
        cursor.execute("SELECT DISTINCT payment_status FROM repair_orders WHERE payment_status IS NOT NULL")
        payment_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Statuts de paiement actuels: {payment_statuses}")
        
        # Vérifier les nouvelles valeurs
        cursor.execute("SELECT DISTINCT status FROM repair_orders WHERE status IS NOT NULL")
        new_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Nouveaux statuts dans repair_orders: {new_statuses}")
        
        # Compter les réparations par statut
        cursor.execute("SELECT status, COUNT(*) FROM repair_orders GROUP BY status")
        status_counts = cursor.fetchall()
        print("Répartition des statuts après migration:")
        for status, count in status_counts:
            print(f"  - {status}: {count} réparations")
        
        # Valider les modifications
        conn.commit()
        print(f"✓ Migration terminée avec succès")
        print(f"✓ {updates_count} réparations mises à jour")
        print(f"✓ {history_updates} entrées d'historique mises à jour")
        return True
        
    except Exception as e:
        # Annuler les modifications en cas d'erreur
        conn.rollback()
        print(f"✗ Erreur lors de la migration: {str(e)}")
        print(f"La sauvegarde est disponible à: {backup_path}")
        return False
        
    finally:
        # Fermer la connexion
        conn.close()

def run_migration():
    """Exécute la migration"""
    # Chemins possibles pour la base de données
    possible_paths = [
        "app.db",
        "data/app.db",
        os.path.join("data", "app.db"),
        os.path.join(os.getcwd(), "app.db"),
        os.path.join(os.getcwd(), "data", "app.db")
    ]
    
    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("Aucune base de données trouvée dans les emplacements suivants:")
        for path in possible_paths:
            print(f"  - {path}")
        return False
    
    print(f"Base de données trouvée: {db_path}")
    return update_repair_statuses(db_path)

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
