from datetime import datetime
from typing import Optional, List, TYPE_CHECKING
from enum import Enum
from pydantic import BaseModel
from decimal import Decimal
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Enum as SQLEnum, Numeric, Text, ForeignKey
from sqlalchemy.orm import relationship
from .base import BaseDBModel, TimestampMixin, BaseModelTimestamp
from .repair_photo import RepairPhotoPydantic
from .repair_note import RepairNotePydantic

# Import conditionnel pour éviter les imports circulaires
if TYPE_CHECKING:
    from .repair_photo import RepairPhotoPydantic
    from .repair_note import RepairNotePydantic

# Énumérations
class RepairStatus(str, Enum):
    PENDING = "pending"  # En attente
    IN_PROGRESS = "in_progress"  # En cours
    ON_HOLD = "on_hold"  # En pause
    COMPLETED = "completed"  # Terminé
    CANCELLED = "cancelled"  # Annulé

class RepairPriority(int, Enum):
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4

class PaymentStatus(str, Enum):
    PENDING = "pending"
    PARTIAL = "partial"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"
    DELIVERED = "delivered"  # Livrée

class PaymentMethod(str, Enum):
    cash = "cash"
    credit_card = "credit_card"
    bank_transfer = "bank_transfer"
    check = "check"
    credit = "credit"  # Paiement à crédit

# Modèles SQLAlchemy
class RepairOrder(BaseDBModel, TimestampMixin):
    __tablename__ = "repair_orders"

    id = Column(Integer, primary_key=True, index=True)
    number = Column(String, unique=True, index=True)
    equipment_id = Column(Integer, ForeignKey("equipment.id"), nullable=True)
    brand = Column(String)  # Marque de l'équipement
    model = Column(String)  # Modèle de l'équipement
    serial_number = Column(String, nullable=True)  # Numéro de série de l'équipement
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=True)  # Référence à la table customers
    customer_name = Column(String)   # Simplifié pour éviter la dépendance à la table customers
    status = Column(SQLEnum(RepairStatus))
    priority = Column(SQLEnum(RepairPriority))
    description = Column(Text)
    reported_issue = Column(Text)
    scheduled_date = Column(DateTime, nullable=True)
    completion_date = Column(DateTime, nullable=True)
    total_cost = Column(Numeric(12, 2), default=0)
    labor_cost = Column(Numeric(12, 2), default=0)
    parts_cost = Column(Numeric(12, 2), default=0)

    # Nouveaux champs financiers
    tax_amount = Column(Numeric(12, 2), default=0)
    discount_amount = Column(Numeric(12, 2), default=0)
    final_amount = Column(Numeric(12, 2), default=0)
    total_paid = Column(Numeric(12, 2), default=0)  # Montant total payé
    payment_status = Column(SQLEnum(PaymentStatus), default=PaymentStatus.PENDING)
    payment_method = Column(SQLEnum(PaymentMethod), nullable=True)
    payment_date = Column(DateTime, nullable=True)
    invoice_number = Column(String, nullable=True)
    invoice_date = Column(DateTime, nullable=True)
    credit_terms = Column(Integer, nullable=True)  # Nombre de jours pour le crédit
    due_date = Column(DateTime, nullable=True)

    technician_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Référence à la table users
    technician_name = Column(String, nullable=True)  # Simplifié pour éviter la dépendance à la table users
    warranty = Column(Boolean, default=False)
    notes = Column(Text, nullable=True)
    customer_signature = Column(String, nullable=True)

    # Relations
    equipment = relationship("Equipment", back_populates="repair_orders")
    customer = relationship("Customer", foreign_keys=[customer_id])
    technician = relationship("User", foreign_keys=[technician_id])
    used_parts = relationship("UsedPart", back_populates="repair_order", cascade="all, delete-orphan")
    payments = relationship("RepairPayment", back_populates="repair_order", cascade="all, delete-orphan")
    technical_notes = relationship("RepairNote", back_populates="repair", cascade="all, delete-orphan")

# Modèle pour les pièces utilisées
class UsedPart(BaseDBModel, TimestampMixin):
    __tablename__ = "used_parts"

    id = Column(Integer, primary_key=True, index=True)
    repair_order_id = Column(Integer, ForeignKey("repair_orders.id"), nullable=False)
    part_id = Column(Integer, ForeignKey("inventory_items.id"), nullable=False)
    quantity = Column(Integer, default=1)
    unit_price = Column(Numeric(12, 2), default=0)
    total_price = Column(Numeric(12, 2), default=0)
    used_at = Column(DateTime, default=datetime.utcnow)
    technician_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    lot_number = Column(String, nullable=True)
    serial_number = Column(String, nullable=True)
    warranty_period = Column(Integer, nullable=True)  # En jours

    # Relations
    repair_order = relationship("RepairOrder", back_populates="used_parts")
    part = relationship("InventoryItem", foreign_keys=[part_id])
    technician = relationship("User", foreign_keys=[technician_id])

# Modèle pour les paiements
class RepairPayment(BaseDBModel, TimestampMixin):
    __tablename__ = "repair_payments"

    id = Column(Integer, primary_key=True, index=True)
    repair_order_id = Column(Integer, ForeignKey("repair_orders.id"), nullable=False)
    amount = Column(Numeric(12, 2), default=0)
    payment_date = Column(DateTime, default=datetime.utcnow)
    payment_method = Column(SQLEnum(PaymentMethod))
    reference_number = Column(String, nullable=True)  # Numéro de référence du paiement
    notes = Column(Text, nullable=True)
    processed_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relations
    repair_order = relationship("RepairOrder", back_populates="payments")
    processor = relationship("User", foreign_keys=[processed_by])

# Modèles Pydantic
class RepairOrderPydantic(BaseModelTimestamp):
    id: Optional[int] = None
    number: str
    equipment_id: Optional[int] = None
    brand: str
    model: str
    serial_number: Optional[str] = None
    customer_id: Optional[int] = None
    customer_name: str
    status: RepairStatus
    priority: RepairPriority
    description: str
    reported_issue: str
    scheduled_date: Optional[datetime] = None
    completion_date: Optional[datetime] = None
    total_cost: Decimal
    labor_cost: Decimal
    parts_cost: Decimal

    # Nouveaux champs financiers
    tax_amount: Decimal = Decimal("0.00")
    discount_amount: Decimal = Decimal("0.00")
    final_amount: Decimal = Decimal("0.00")
    total_paid: Decimal = Decimal("0.00")  # Montant total payé
    payment_status: PaymentStatus = PaymentStatus.PENDING
    payment_method: Optional[PaymentMethod] = None
    payment_date: Optional[datetime] = None
    invoice_number: Optional[str] = None
    invoice_date: Optional[datetime] = None
    credit_terms: Optional[int] = None
    due_date: Optional[datetime] = None

    technician_id: Optional[int] = None
    technician_name: Optional[str] = None
    warranty: bool
    notes: Optional[str] = None
    customer_signature: Optional[str] = None

    # Relations
    used_parts: List["UsedPartPydantic"] = []
    payments: List["RepairPaymentPydantic"] = []
    photos: List["RepairPhotoPydantic"] = []
    technical_notes: List["RepairNotePydantic"] = []

    class Config:
        from_attributes = True

class UsedPartPydantic(BaseModelTimestamp):
    id: Optional[int] = None
    repair_order_id: int
    part_id: int
    quantity: int = 1
    unit_price: Decimal = Decimal("0.00")
    total_price: Decimal = Decimal("0.00")
    used_at: datetime = datetime.utcnow()
    technician_id: Optional[int] = None
    lot_number: Optional[str] = None
    serial_number: Optional[str] = None
    warranty_period: Optional[int] = None

    class Config:
        from_attributes = True

class RepairPaymentPydantic(BaseModelTimestamp):
    id: Optional[int] = None
    repair_order_id: int
    amount: Decimal
    payment_date: datetime = datetime.utcnow()
    payment_method: PaymentMethod
    reference_number: Optional[str] = None
    notes: Optional[str] = None
    processed_by: Optional[int] = None

    class Config:
        from_attributes = True